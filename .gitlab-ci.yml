variables:
  REPORT_DIR: "${CI_PROJECT_DIR}/.report" # Defines the location of the analysis task cache
  GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  SONAR_SERVER: https://sonarqube.corp.algento.com
  CICD_BUILD_IMAGE: "acrsvcprdagtaen001.azurecr.io/cicd/build_rust_bookworm:0.1.2"

before_script:
  - export PROJECT=`echo $CI_COMMIT_TAG | sed -E 's/^[^-]+-//; s/-[^-]+$//'`
  - export CACHE_DIR=/cache
  - export TEST_REPO=acruatprdagtaen001.azurecr.io
  - export GROUP=bot4
  - export DOCKER_FILE=rust.Dockerfile

stages:
  - generate
  - scan
  - build

generate-report:
  image:
    name: ${CICD_BUILD_IMAGE}
  stage: generate
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'develop'
    - if: $CI_COMMIT_BRANCH == 'stage'
    - if: $CI_COMMIT_BRANCH == 'main'
  script:
    - mkdir -p ${REPORT_DIR}
    - cargo clippy --message-format=json &> ${REPORT_DIR}/clippy-report.json
    - cargo tarpaulin --ignore-tests --all-features --release --out Lcov --output-dir ${REPORT_DIR}
  allow_failure: true
  cache:
    key: ${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}
    paths:
      - ${REPORT_DIR}
  tags:
    - botim4

sonar-scan:
  image:
    name: sonarsource/sonar-scanner-cli:11.3
    entrypoint: ["/bin/sh", "-c"]
  stage: scan
  cache:
    key: ${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}
    paths:
      - ${REPORT_DIR}
  script:
    - sonar-scanner  -Dsonar.projectKey=${CI_PROJECT_NAME} -Dcommunity.rust.clippy.reportPaths=${REPORT_DIR}/clippy-report.json -Dsonar.host.url=${SONAR_SERVER} -Dsonar.token=${SONARQUBE_CICD_TOKEN} -Dcommunity.rust.lcov.reportPaths=${REPORT_DIR}/lcov.info
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'develop'
    - if: $CI_COMMIT_BRANCH == 'stage'
    - if: $CI_COMMIT_BRANCH == 'main'
  tags:
    - botim4

build:
  image:
    name: "gcr.io/kaniko-project/executor:debug"
    entrypoint: ["/bin/sh", "-c"]
  stage: build
  only:
    - tags
  script:
    - /kaniko/executor --context=. --dockerfile=/cicd/templates/${DOCKER_FILE} --skip-push-permission-check --cache-run-layers=false  --cache-dir=${CACHE_DIR}/image --build-arg APP=${PROJECT} --destination=${TEST_REPO}/${GROUP}/${PROJECT}:$CI_COMMIT_TAG
  tags:
    - botim4
