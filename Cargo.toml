[workspace]
members = ["at-friendship-*"]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2024"
authors = ["Botim4.0 Team"]
repository = "https://gitlab.corp.algento.com/botim-4.0/user/at-friendship"
homepage = "https://gitlab.corp.algento.com/botim-4.0/user/at-friendship"
description = "AstraTech FriendShip Service"
license = "None"

[workspace.dependencies]
## Dependencies Manager
at-deps = { git = "ssh://***************************/botim-4.0/at-dep-mgmt", features = [
  "at-friendship",
] }
at-internal-deps = { git = "ssh://***************************/botim-4.0/at-dep-mgmt", features = [
  "at-friendship",
] }

at-janus-server = { git = "ssh://***************************/botim-4.0/atproto/at-janus", branch = "develop" }

at-janus-client = { git = "ssh://***************************/botim-4.0/atproto/at-janus", branch = "develop" }

at-friendship-stub = { git = "ssh://***************************/botim-4.0/at-bss" }

at-friendship-protocol-pb = { git = "ssh://***************************/botim-4.0/at-bss" }

at-msg-receiver-stub = { git = "ssh://***************************/botim-4.0/at-bss" }

at-msg-protocol-pb = { git = "ssh://***************************/botim-4.0/at-bss" }

at-auid-stub = { git = "ssh://***************************/botim-4.0/at-bss" }

at-auid-protocol-pb = { git = "ssh://***************************/botim-4.0/at-bss" }

## internal dependencies
at-friendship-domain-model = { path = "./at-friendship-domain-model" }
at-friendship-repo = { path = "./at-friendship-repo" }
at-friendship-integration = { path = "./at-friendship-integration" }
at-friendship-server-common = { path = "./at-friendship-server-common" }

## Exceptional Dependencies
sqlx = { version = "0.8", features = ["runtime-tokio", "mysql", "chrono"] }
tonic = "0.12.3"
prost = "0.13"
futures = "0.3.31"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0"
hex = "0.4.3"
lapin = "2.3"
fred = "10.1.0"
tracing = "*"
reqwest = { version = "0.12.22", features = ["json"] }