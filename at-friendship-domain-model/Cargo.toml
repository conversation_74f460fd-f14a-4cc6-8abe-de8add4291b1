[package]
name = "at-friendship-domain-model"
version.workspace = true
edition.workspace = true
authors.workspace = true
repository.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true

[dependencies]
# dep-mgmt
at-deps = { workspace = true }
at-internal-deps = { workspace = true }

## internal dependencies

## exceptional
tracing = { workspace = true }
sqlx = { workspace = true }
fred = { workspace = true }
