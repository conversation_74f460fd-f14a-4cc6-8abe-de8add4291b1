use at_deps::at_friendship::*;

use {crate::all::*,
     async_trait::async_trait};

#[async_trait]
pub trait BlockedUserApplication: Send + Sync + 'static {
  // block or unblock user, change the status of the block user
  async fn save_blocked_user(
    &self,
    user_id: &BotimUserId,
    blocked_user_id: &BotimUserId,
    status: BlockUserStatus,
  ) -> FriendshipResult<()>;
  // get blocked users
  async fn get_blocked_users(&self, user_id: &BotimUserId) -> FriendshipResult<Vec<BlockedUserEntity>>;
  // check if a user is blocked by another user
  async fn is_blocked(&self, user_id: &BotimUserId, blocked_user_id: &BotimUserId) -> FriendshipResult<bool>;
  // check if a user is blocked by multiple users
  async fn is_blocked_by_users(
    &self,
    user_ids: &[BotimUserId],
    blocked_user_id: &BotimUserId,
  ) -> FriendshipResult<Vec<BotimUserId>>;
}

mod default;
pub use default::*;
