use {crate::all::*,
     async_trait::async_trait,
     at_deps::at_friendship::*};

use tracing::{error,
              info};

#[async_trait]
pub trait DefaultBlockedUserApplication: Send + Sync + 'static {
  fn deps(&self) -> &dyn <PERSON><PERSON>ultBlockedUserApplicationDependencies;

  #[tracing::instrument(
    name = "DefaultBlockedUserApplication::save_blocked_user",
    skip(self, user_id, blocked_user_id, status)
  )]
  async fn save_blocked_user(
    &self,
    user_id: &BotimUserId,
    blocked_user_id: &BotimUserId,
    status: BlockUserStatus,
  ) -> FriendshipResult<()> {
    info!("User {} blocked user {}", user_id, blocked_user_id);
    let blocked_user_entity = BlockedUserFactory::create_blocked_user_entity(user_id, blocked_user_id, status);

    self
      .deps()
      .blocked_user_repo()
      .save_blocked_user_entity(&blocked_user_entity)
      .await
      .inspect(|_| {
        info!(
          "Blocked user entity saved: {:?} for user: {}",
          blocked_user_entity, user_id
        );
      })
      .inspect_err(|e| {
        error!("Failed to save blocked user entity: {:?} for user: {}", e, user_id);
      })?;
    info!(
      "Blocked user entity saved: {:?} for user: {}",
      blocked_user_entity, user_id
    );
    Ok(())
  }

  #[tracing::instrument(name = "DefaultBlockedUserApplication::get_blocked_users", skip(self, user_id))]
  async fn get_blocked_users(&self, user_id: &BotimUserId) -> FriendshipResult<Vec<BlockedUserEntity>> {
    info!("Get blocked users for user: {}", user_id);
    let blocked_user_entities = self
      .deps()
      .blocked_user_repo()
      .get_blocked_user_entities(user_id)
      .await
      .inspect(|_| {
        info!("Returned {} blocked user entities", user_id);
      })
      .inspect_err(|e| {
        error!("Failed to get blocked user entities: {:?}", e);
      })?;
    info!(
      "Returned {} blocked user entities for user: {}",
      blocked_user_entities.len(),
      user_id
    );
    Ok(blocked_user_entities)
  }

  async fn is_blocked(&self, user_id: &BotimUserId, blocked_user_id: &BotimUserId) -> FriendshipResult<bool> {
    info!("Check if user {} is blocked by {}", user_id, blocked_user_id);
    self
      .deps()
      .blocked_user_repo()
      .is_blocked(user_id, blocked_user_id)
      .await
      .inspect(|_| {
        info!("User {} is blocked by {}", user_id, blocked_user_id);
      })
      .inspect_err(|e| {
        error!(
          "Failed to check if user {} is blocked by {}: {:?}",
          user_id, blocked_user_id, e
        );
      })
  }

  async fn is_blocked_by_users(
    &self,
    user_ids: &[BotimUserId],
    blocked_user_id: &BotimUserId,
  ) -> FriendshipResult<Vec<BotimUserId>> {
    info!("Check if users {:?} are blocked by {}", user_ids, blocked_user_id);
    let block_user_ids = self
      .deps()
      .blocked_user_repo()
      .is_blocked_by_users(user_ids, blocked_user_id)
      .await
      .inspect(|_| {
        info!("Users {:?} are blocked by {}", user_ids, blocked_user_id);
      })
      .inspect_err(|e| {
        error!(
          "Failed to check if users {:?} are blocked by {}: {:?}",
          user_ids, blocked_user_id, e
        );
      })?;
    info!("Users {:?} are blocked by {}", user_ids, blocked_user_id);
    Ok(block_user_ids)
  }
}

#[async_trait]
impl<T: DefaultBlockedUserApplication> BlockedUserApplication for T {
  async fn save_blocked_user(
    &self,
    user_id: &BotimUserId,
    blocked_user_id: &BotimUserId,
    status: BlockUserStatus,
  ) -> FriendshipResult<()> {
    DefaultBlockedUserApplication::save_blocked_user(self, user_id, blocked_user_id, status).await
  }

  async fn get_blocked_users(&self, user_id: &BotimUserId) -> FriendshipResult<Vec<BlockedUserEntity>> {
    DefaultBlockedUserApplication::get_blocked_users(self, user_id).await
  }

  async fn is_blocked(&self, user_id: &BotimUserId, blocked_user_id: &BotimUserId) -> FriendshipResult<bool> {
    DefaultBlockedUserApplication::is_blocked(self, user_id, blocked_user_id).await
  }

  async fn is_blocked_by_users(
    &self,
    user_ids: &[BotimUserId],
    blocked_user_id: &BotimUserId,
  ) -> FriendshipResult<Vec<BotimUserId>> {
    DefaultBlockedUserApplication::is_blocked_by_users(self, user_ids, blocked_user_id).await
  }
}

#[async_trait]
pub trait DefaultBlockedUserApplicationDependencies: Send + Sync + 'static {
  fn blocked_user_repo(&self) -> &dyn BlockedUserRepo;
}
