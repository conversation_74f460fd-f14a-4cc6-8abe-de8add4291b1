use {crate::all::*,
     async_trait::async_trait,
     at_deps::at_friendship::*};

#[async_trait]
pub trait BotimUserApplication: Send + Sync + 'static {
  async fn get_owned_botim_users(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
    task_id: &Option<String>,
  ) -> FriendshipResult<(Vec<BotimUserEntity>, bool)>;

  async fn save_botim_user(&self, botim_user_entities: &[BotimUserEntity]) -> FriendshipResult<()>;

  async fn new_botim_user_notification(
    &self,
    botim_user_entity: &BotimUserEntity,
    register_event: RegisterEvent,
  ) -> FriendshipResult<()>;

  /// Save botim users in background
  async fn save_botim_users_in_background(&self, phone_numbers: &[PhoneNumber]) -> FriendshipResult<String>;
}

mod default;
pub use default::*;
