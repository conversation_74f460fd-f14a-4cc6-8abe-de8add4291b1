use {crate::all::*,
     async_trait::async_trait,
     at_deps::at_friendship::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::id::uuid::UuidBytes,
     std::sync::Arc,
     tracing::{error,
               info}};

#[async_trait]
pub trait DefaultBotimUserApplication: Clone + Send + Sync + 'static {
  /// deps
  fn deps(&self) -> Arc<dyn DefaultBotimUserApplicationDependencies>;

  /// Get owned botim users
  /// 1. Get botim user entities by auids from database
  /// 2. Check if the botim user entities are synchronized with the auid service
  /// 3. Return the botim user entities and the synchronization status
  #[tracing::instrument(name = "get_owned_botim_users", skip(self, owner_id, phone_number_filters, task_id))]
  async fn get_owned_botim_users(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
    task_id: &Option<String>,
  ) -> FriendshipResult<(Vec<BotimUserEntity>, bool)> {
    info!("Get owned botim users for: {:?}", owner_id);

    // 1. First, get the phone numbers that actually exist in the owner's contacts
    let existing_phone_numbers = self
      .deps()
      .contact_repo()
      .get_existing_contact_phone_numbers(owner_id, phone_number_filters)
      .await
      .inspect(|existing| {
        info!(
          "Found {} existing contact phone numbers for user: {} out of {} requested",
          existing.len(),
          owner_id,
          phone_number_filters.len()
        );
      })
      .inspect_err(|e| {
        error!(
          "Failed to get existing contact phone numbers for user: {} with phone numbers: {:?} error: {:?}",
          owner_id, phone_number_filters, e
        );
      })?;

    // If no phone numbers exist in contacts, return empty result
    if existing_phone_numbers.is_empty() {
      info!("No existing contact phone numbers found for user: {}", owner_id);
      return Ok((Vec::new(), false));
    }

    // 2. Get botim user entities by the existing phone numbers
    let botim_users = self
      .deps()
      .botim_user_repo()
      .get_auids_by_phone_numbers(&existing_phone_numbers)
      .await
      .inspect(|users| {
        info!(
          "Get {} botim users for {} existing phone numbers for user: {}",
          users.len(),
          existing_phone_numbers.len(),
          owner_id
        );
      })
      .inspect_err(|e| {
        error!(
          "Failed to get botim users for user: {} with existing phone numbers: {:?} error: {:?}",
          owner_id, existing_phone_numbers, e
        );
      })?;

    // 3. Check if the botim user entities are synchronized with the auid service
    let task_status: bool = match task_id {
      Some(task_id) => self
        .deps()
        .task_mgmt_repo()
        .get_task(task_id)
        .await
        .inspect(|_| {
          info!("Got task status for task: {} for user: {}", task_id, owner_id);
        })
        .inspect_err(|e| {
          error!(
            "Failed to get task status: {:?} for task: {} for user: {}",
            e, task_id, owner_id
          );
        })?,
      None => false,
    };

    info!(
      "Returned {} botim users for user: {} with {} existing phone numbers, task status: {:?}",
      botim_users.len(),
      owner_id,
      existing_phone_numbers.len(),
      task_status
    );
    Ok((botim_users, task_status))
  }

  /// Process phone numbers and save botim users in background
  /// 1. Start a background task to process the phone numbers
  /// 2. Add a task to the task management service
  /// 3. Get auids by phone numbers from auid service
  /// 4. Create botim user entities
  /// 5. Save botim user entities to database
  /// 6. Delete the background task After the task is completed
  #[tracing::instrument(name = "save_botim_users_in_background", skip(self, phone_numbers))]
  async fn save_botim_users_in_background(&self, phone_numbers: &[PhoneNumber]) -> FriendshipResult<String> {
    info!("Processing save botim users for {} phone numbers", phone_numbers.len());

    let self_clone = self.clone();
    let phone_numbers_clone = phone_numbers.to_vec();
    let count = phone_numbers.len();

    info!(
      "Submitting background task for processing for {} phone numbers",
      phone_numbers.len()
    );

    // 1. Start a background task to process the phone numbers
    let task_id = UuidBytes::generate().to_string();
    let task_id_clone = task_id.clone();
    tokio::spawn(async move {
      info!(
        "Getting auids for {} phone numbers in background task {}",
        phone_numbers_clone.len(),
        task_id_clone
      );

      // 2. Add a task to the task management service
      info!("Adding task in background: {}", task_id_clone);
      self_clone
        .deps()
        .task_mgmt_repo()
        .add_task(&task_id_clone)
        .await
        .inspect(|_| {
          info!("Added task in background: {} successfully", task_id_clone);
        })
        .inspect_err(|e| {
          error!("Failed to add task in background: {:?} for task: {}", e, task_id_clone);
        })
        .ok();

      // 3. Get auids by phone numbers from auid service
      let auids = match self_clone
        .deps()
        .auid_repo()
        .get_auids_by_phone_numbers(&phone_numbers_clone)
        .await
        .inspect(|_| {
          info!(
            "Got auids for {} phone numbers in background",
            phone_numbers_clone.len()
          );
        })
        .map_err(|e| {
          error!(
            "Failed to get auids in background for {} phone numbers: {:?}",
            phone_numbers_clone.len(),
            e
          );
          "Failed to get auids".to_string()
        }) {
        Ok(auids) => auids,
        Err(_) => return,
      };

      // 4. Create botim user entities
      info!("Creating botim user entities for {} auids in background", auids.len());
      let botim_user_entities = match BotimUserServiceFactory::create_botim_user_entities(auids.clone())
        .inspect(|_| {
          info!(
            "Successfully created botim user entities for {} auids in background",
            auids.len()
          );
        })
        .map_err(|e| {
          error!(
            "Failed to create botim user entities for {} auids in background: {:?}",
            auids.len(),
            e
          );
          "Failed to create botim user entities".to_string()
        }) {
        Ok(entities) => entities,
        Err(_) => return,
      };

      // 5. Save botim user entities to database
      info!(
        "Saving botim user entities to database for {} entities in background",
        botim_user_entities.len()
      );
      let save_result = self_clone
        .deps()
        .botim_user_repo()
        .save_botim_user_entities_idempotently(&botim_user_entities)
        .await
        .inspect(|_| {
          info!(
            "Successfully saved botim user entities to database for {} entities in background",
            botim_user_entities.len()
          );
        })
        .map_err(|e| {
          error!(
            "Failed to save botim user entities for {} entities in background: {:?}",
            botim_user_entities.len(),
            e
          );
          "Failed to save botim user entities in background".to_string()
        });

      // 6. Delete the background task After the task is completed
      info!(
        "Deleting task: {} in background for {} phone numbers",
        task_id_clone, count
      );
      self_clone
        .deps()
        .task_mgmt_repo()
        .delete_task(&task_id_clone)
        .await
        .inspect(|_| {
          info!("Deleted task: {} successfully in background", task_id_clone);
        })
        .inspect_err(|e| {
          error!(
            "Failed to delete task: {:?} for task: {} in background",
            e, task_id_clone
          );
        })
        .ok();

      info!(
        "Completed processing {} phone numbers, auids: {}, save_result: {:?} in background",
        count,
        auids.len(),
        save_result
      );
    })
    .await
    .map_err(|e| {
      error!("Failed to submit background task: {:?}", e);
      FriendshipError::AsyncTaskPool(format!("Failed to submit background task: {e:?}"))
    })?;

    info!(
      "Submitted background task for processing for {} phone numbers and task {}",
      phone_numbers.len(),
      task_id,
    );
    Ok(task_id.to_string())
  }

  #[tracing::instrument(name = "save_botim_user", skip(self, botim_user_entities))]
  async fn save_botim_user(&self, botim_user_entities: &[BotimUserEntity]) -> FriendshipResult<()> {
    info!("Save botim users for: {}", botim_user_entities.len());

    // 0. check param validity
    info!("Checking param validity for {:?}", botim_user_entities);
    BotimUserServiceFactory::check_save_botim_user_entities_param_validity(botim_user_entities)
      .inspect(|_| {
        info!("Param validity checked for {:?}", botim_user_entities);
      })
      .inspect_err(|e| {
        error!("Failed to check param validity: {:?}", e);
      })?;
    info!("Param validity checked for {:?}", botim_user_entities);

    // 1. save botim user entities to database
    info!("Saving botim users to database for {:?}", botim_user_entities);
    self
      .deps()
      .botim_user_repo()
      .save_botim_user_entities_idempotently(botim_user_entities)
      .await
      .inspect(|_| {
        info!(
          "Successfully saved botim users to database for {} entities",
          botim_user_entities.len()
        );
      })
      .inspect_err(|e| {
        error!("Failed to save botim users: {:?}", e);
      })?;
    Ok(())
  }

  #[tracing::instrument(name = "delete_botim_user", skip(self, botim_user_id))]
  async fn delete_botim_user(&self, botim_user_id: &BotimUserId) -> FriendshipResult<()> {
    info!("Delete botim user for: {:?}", botim_user_id);
    self
      .deps()
      .botim_user_repo()
      .delete_botim_user(botim_user_id)
      .await
      .inspect(|_| {
        info!("Deleted botim user: {:?}", botim_user_id);
      })
      .inspect_err(|e| {
        error!("Failed to delete botim user: {:?}", e);
      })?;
    Ok(())
  }

  /// Send notification to all the contact owners who has the same phone number
  /// 1. Get all the contact owners who has the same phone number
  /// 2. Convert contact owners to auids
  /// 3. Get all the botim user entities by auids
  /// 4. Get all the botim user reprs from the botim user entities
  /// 5. Create notification entity
  /// 6. Send notification to all the contact owners
  #[tracing::instrument(name = "new_botim_user_notification", skip(self, botim_user_entity, register_event))]
  async fn new_botim_user_notification(
    &self,
    botim_user_entity: &BotimUserEntity,
    register_event: RegisterEvent,
  ) -> FriendshipResult<()> {
    info!("New botim user notification for: {:?}", botim_user_entity.botim_user_id);

    // 1. Get all the contact owners who has the same phone number
    let contact_owners = self
      .deps()
      .contact_repo()
      .get_contact_owners_with_friend_phone_number(&botim_user_entity.phone_number)
      .await
      .inspect(|_| {
        info!(
          "Found contact owners with phone number: {}",
          botim_user_entity.phone_number
        );
      })
      .inspect_err(|e| {
        error!("Failed to get contact owners: {:?}", e);
      })?;

    // 2. Convert contact owners to auids
    let botim_user_ids = BotimUserServiceFactory::convert_contact_owners_to_botim_user_ids(contact_owners.clone())
      .map_err(|e| {
        error!("Failed to convert contact owners to auids: {:?}", e);
        e
      })?;

    if botim_user_ids.is_empty() {
      info!("No auids found for {} contact owners", contact_owners.len());
      return Ok(());
    }

    // 3. Get all the botim user entities by auids
    let botim_user_entities = self
      .deps()
      .botim_user_repo()
      .get_botim_user_entities_by_auids(&botim_user_ids)
      .await
      .inspect(|_| {
        info!("Found botim user entities for contact owners: {}", contact_owners.len());
      })
      .inspect_err(|e| {
        error!("Failed to get botim user entities: {:?}", e);
      })?;

    // 4. Get all the botim user reprs from the botim user entities
    info!(
      "Getting botim user reprs for {} botim user entities",
      botim_user_entities.len()
    );
    let botim_user_reprs =
      BotimUserServiceFactory::get_botim_user_repr_vector(botim_user_entities).inspect_err(|e| {
        error!("Failed to get botim user reprs: {:?}", e);
      })?;

    // 5. Create notification entity
    info!(
      "Creating notification entity for {} botim user reprs",
      botim_user_reprs.len()
    );
    let notification_entity = NotificationFactory::create_register_notification_entity(
      botim_user_reprs,
      register_event,
      botim_user_entity.phone_number.clone(),
      botim_user_entity.auid_repr.clone(),
      botim_user_entity.botim_user_id.clone(),
    );

    // 6. Send notification to all the contact owners
    self
      .deps()
      .notification_repo()
      .push_notification(&notification_entity)
      .await
      .inspect(|_| {
        info!(
          "Successfully sent notification to contact owners: {}",
          notification_entity.receiver.len()
        );
      })
      .inspect_err(|e| {
        error!("Failed to send notification: {:?}", e);
      })?;

    Ok(())
  }
}

#[async_trait]
pub trait DefaultBotimUserApplicationDependencies: Send + Sync + 'static {
  fn botim_user_repo(&self) -> &dyn BotimUserRepo;
  fn auid_repo(&self) -> &dyn AuidRepo;
  fn contact_repo(&self) -> &dyn ContactRepo;
  fn notification_repo(&self) -> &dyn NotificationRepo;
  fn task_mgmt_repo(&self) -> &dyn TaskMgmtRepo;
}

#[async_trait]
impl<T: DefaultBotimUserApplication> BotimUserApplication for T {
  async fn get_owned_botim_users(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
    task_id: &Option<String>,
  ) -> FriendshipResult<(Vec<BotimUserEntity>, bool)> {
    DefaultBotimUserApplication::get_owned_botim_users(self, owner_id, phone_number_filters, task_id).await
  }

  async fn save_botim_user(&self, botim_user_entities: &[BotimUserEntity]) -> FriendshipResult<()> {
    DefaultBotimUserApplication::save_botim_user(self, botim_user_entities).await
  }

  async fn new_botim_user_notification(
    &self,
    botim_user_entity: &BotimUserEntity,
    register_event: RegisterEvent,
  ) -> FriendshipResult<()> {
    DefaultBotimUserApplication::new_botim_user_notification(self, botim_user_entity, register_event).await
  }

  async fn save_botim_users_in_background(&self, phone_numbers: &[PhoneNumber]) -> FriendshipResult<String> {
    DefaultBotimUserApplication::save_botim_users_in_background(self, phone_numbers).await
  }
}
