use at_deps::at_friendship::*;

use async_trait::async_trait;

use crate::{all::*,
            value::contact::ContactOperation};

#[async_trait]
pub trait ContactsApplication: Send + Sync + 'static {
  /// Sync contacts
  async fn sync_contacts(&self, contact_operation: ContactOperation) -> FriendshipResult<Vec<PhoneNumber>>;
  /// get existing contacts with phone number filters
  async fn get_existing_contact_phone_numbers(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<PhoneNumber>>;

  async fn get_existing_contact_owners(
    &self,
    botim_user_id: &BotimUserId,
    owner_ids: &[ContactOwnerId],
  ) -> FriendshipResult<Vec<ContactOwnerId>>;
}

mod default;
pub use default::*;
