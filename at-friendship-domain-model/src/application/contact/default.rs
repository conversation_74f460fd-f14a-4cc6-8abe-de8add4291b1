use {super::*,
     crate::service::contact::factory::ContactFactory,
     async_trait::async_trait,
     tracing::{error,
               info}};

#[async_trait]
pub trait DefaultContactsApplication: Send + Sync + 'static {
  fn deps(&self) -> &dyn ContactsApplicationDependencies;

  /// Sync contacts
  /// 1. Create contact entities
  /// 2. Update contact entities for the owner
  /// 3. Get phone number filters
  /// 4. Return phone number filters (Will be used to get auids in botim user application in background)
  #[tracing::instrument(name = "sync_contacts", skip(self, contact_operation))]
  async fn sync_contacts(&self, contact_operation: ContactOperation) -> FriendshipResult<Vec<PhoneNumber>> {
    info!(
      "sync contacts for user: {:?}, add: {}, delete: {}, update: {}",
      contact_operation.owner_id,
      contact_operation.add.len(),
      contact_operation.delete.len(),
      contact_operation.update.len()
    );

    // 0. check param validity
    info!("Checking param validity for {:?}", contact_operation.owner_id);
    ContactFactory::check_param_validity(&contact_operation)
      .inspect(|_| {
        info!("Param validity checked for {:?}", contact_operation.owner_id);
      })
      .inspect_err(|e| {
        error!(
          "check_param_validity error: {:?} for {:?}",
          e, contact_operation.owner_id
        );
      })?;
    info!("Param validity checked for {:?}", contact_operation.owner_id);

    // 1. create Contact Entity
    info!("Creating contact entities for {:?}", contact_operation.owner_id);
    let contact_entities = ContactFactory::create_contact_entities(&contact_operation)
      .inspect(|_| {
        info!(
          "Successfully created contact entities for {:?}",
          contact_operation.owner_id
        );
      })
      .inspect_err(|e| {
        error!(
          "create_contact_entities error: {:?} for {:?}",
          e, contact_operation.owner_id
        );
      })?;

    // 2. update the contact of the owner
    info!("Updating contact entities for {:?}", contact_operation.owner_id);
    self
      .deps()
      .contact_repo()
      .update_contacts_for_owner_idempotentially(&contact_operation.owner_id, &contact_entities)
      .await
      .inspect(|_| {
        info!(
          "Successfully updated contact entities for {:?}",
          contact_operation.owner_id
        );
      })
      .inspect_err(|e| error!("update_contacts_for_owner error: {:?}", e))?;

    // 3. get phone number filters
    info!("Getting phone number filters for {:?}", contact_operation.owner_id);
    let phone_number_filters = ContactFactory::get_phone_number_filters(&contact_entities);
    info!("Phone number filters: {:?}", phone_number_filters.len());
    Ok(phone_number_filters)
  }

  #[tracing::instrument(
    name = "get_existing_contact_phone_numbers",
    skip(self, owner_id, phone_number_filters)
  )]
  async fn get_existing_contact_phone_numbers(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<PhoneNumber>> {
    info!(
      "Getting existing contact phone numbers for {:?}, phone number filters: {:?}",
      owner_id,
      phone_number_filters.len()
    );

    let contact_entities = self
      .deps()
      .contact_repo()
      .get_existing_contact_phone_numbers(owner_id, phone_number_filters)
      .await
      .inspect(|_| {
        info!("Successfully got existing contact phone numbers for {:?}", owner_id);
      })
      .inspect_err(|e| error!("get_existing_contact_phone_numbers error: {:?}", e))?;
    info!("Existing contact phone numbers: {:?}", contact_entities.len());
    Ok(contact_entities)
  }

  #[tracing::instrument(name = "get_existing_contact_owners", skip(self, botim_user_id, owner_ids))]
  async fn get_existing_contact_owners(
    &self,
    botim_user_id: &BotimUserId,
    owner_ids: &[ContactOwnerId],
  ) -> FriendshipResult<Vec<ContactOwnerId>> {
    info!(
      "Getting existing contact owner ids for {:?}, owner ids: {:?}",
      botim_user_id,
      owner_ids.len()
    );

    // 0. check param validity
    info!("Checking param validity for {:?}", botim_user_id);
    ContactFactory::check_get_existing_contact_owners_param_validity(owner_ids)
      .inspect(|_| {
        info!("Param validity checked for {:?}", botim_user_id);
      })
      .inspect_err(|e| error!("check_get_existing_contact_owners_param_validity error: {:?}", e))?;
    info!("Param validity checked for {:?}", botim_user_id);

    // 1. get botim user entities by auids
    let phone_numbers = self
      .deps()
      .botim_user_repo()
      .get_botim_user_entities_by_auids(&[botim_user_id.clone()])
      .await
      .inspect(|_| {
        info!("Successfully got existing contact by auids for {:?}", botim_user_id);
      })
      .inspect_err(|e| error!("get_existing_contact_by_botim_user_id error: {:?}", e))?;
    info!("Existing contact phone numbers: {:?}", phone_numbers);

    // 2. Early return if no phone numbers found
    let Some(first_entity) = phone_numbers.first() else {
      info!("No botim user entities found for {:?}", botim_user_id);
      return Ok(Vec::new());
    };

    // 3. get contact entities by phone numbers
    let contact_phone_numbers = self
      .deps()
      .contact_repo()
      .get_contact_owners_with_friend_phone_number_and_owner_ids(&first_entity.phone_number, owner_ids)
      .await
      .inspect(|_| {
        info!(
          "Successfully got contact entities by phone numbers for {:?}",
          botim_user_id
        );
      })
      .inspect_err(|e| error!("get_contact_entities_by_phone_numbers error: {:?}", e))?;
    info!("Contact entities: {:?}", contact_phone_numbers);

    Ok(contact_phone_numbers)
  }
}

mod dependencies;
pub use dependencies::*;

#[async_trait]
impl<T: DefaultContactsApplication> ContactsApplication for T {
  async fn sync_contacts(&self, contact_operation: ContactOperation) -> FriendshipResult<Vec<PhoneNumber>> {
    DefaultContactsApplication::sync_contacts(self, contact_operation).await
  }

  async fn get_existing_contact_phone_numbers(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<PhoneNumber>> {
    DefaultContactsApplication::get_existing_contact_phone_numbers(self, owner_id, phone_number_filters).await
  }

  async fn get_existing_contact_owners(
    &self,
    botim_user_id: &BotimUserId,
    owner_ids: &[ContactOwnerId],
  ) -> FriendshipResult<Vec<ContactOwnerId>> {
    DefaultContactsApplication::get_existing_contact_owners(self, botim_user_id, owner_ids).await
  }
}
