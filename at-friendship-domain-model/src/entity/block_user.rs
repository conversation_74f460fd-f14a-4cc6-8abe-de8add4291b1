use at_internal_deps::at_friendship::*;

use {crate::value::{block_user::*,
                    botim_user::*},
     at_lib_comm_domain::value::time::{CreateAt,
                                       UpdateAt}};

#[derive(<PERSON><PERSON>, Debug)]
pub struct BlockedUserEntity {
  pub user_id:         BotimUserId,
  pub blocked_user_id: BotimUserId,
  pub status:          BlockUserStatus,
  pub created_at:      CreateAt,
  pub updated_at:      UpdateAt,
}
