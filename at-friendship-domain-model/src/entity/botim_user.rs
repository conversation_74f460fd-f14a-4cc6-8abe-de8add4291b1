use at_internal_deps::at_friendship::*;

use at_lib_comm_domain::value::time::{CreateAt,
                                      UpdateAt};

use crate::{all::PhoneNumber,
            value::{botim_user::BotimUserId,
                    contact::AuidRepr}};

/// Botim User Entity
///
/// It's a mapping to the botim user in `AT-AUID` system, with some credentials:
/// 1. Phone Number
/// 2. Auid (just the BotimUserId, which wrapping Auid)
/// 3. Auid Repr
/// 4. Email ID (in future)
#[derive(Clone, Debug)]
pub struct BotimUserEntity {
  /// Contact ID
  pub botim_user_id: BotimUserId,
  /// The Auid Repr of the botim user
  pub auid_repr:     AuidRepr,
  /// The phone number of the botim user
  pub phone_number:  PhoneNumber,
  /// Created at
  pub created_at:    CreateAt,
  /// Updated at
  pub updated_at:    UpdateAt,
}
