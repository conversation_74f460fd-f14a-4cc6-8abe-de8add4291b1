use at_internal_deps::at_friendship::*;

use at_lib_comm_domain::value::time::{CreateAt,
                                      UpdateAt};

use crate::{all::*,
            value::contact::{ContactId,
                             ContactName,
                             ContactStatus,
                             PhoneNumber}};

#[derive(<PERSON><PERSON>, Debug)]
pub struct ContactEntity {
  /// Contact ID
  pub contact_id:   ContactId,
  /// The owner of the contact
  pub owner_id:     ContactOwnerId,
  /// The name of the contact
  pub contact_name: Option<ContactName>,
  /// The phone number of the contact
  pub phone_number: PhoneNumber,
  /// The status of the contact
  pub status:       ContactStatus,
  /// Created at
  pub created_at:   CreateAt,
  /// Updated at
  pub updated_at:   UpdateAt,
}
