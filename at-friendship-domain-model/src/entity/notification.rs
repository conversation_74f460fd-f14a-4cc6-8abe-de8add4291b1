use {crate::all::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::value::time::CreateAt};

/// Notification for Botim users
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct BotimUserNotificationEntity {
  /// receiver
  pub receiver:   Vec<AuidRepr>,
  /// Status of the user (0 = Registered, 1 = Unregistered)
  pub status:     RegisterEvent,
  /// Botim user information
  pub botim_user: Option<BotimUser>,
  pub created_at: CreateAt,
}
