use at_deps::at_friendship::*;

pub mod secure_error;

/// Friendship error types
#[derive(Debug, thiserror::Erro<PERSON>)]
pub enum FriendshipError {
  #[error("Database error: {0}")]
  DatabaseError(String),

  #[error("Unknown error: {0}")]
  Unknown(String),

  #[error("Invalid input: {0}")]
  InvalidInput(String),

  #[error("repo error: {0}")]
  RepoError(String),

  #[error("Downstream error: {0}")]
  Downstream(String),

  #[error("Deserialization error: {0}")]
  Deserialization(String),

  #[error("Invalid user id: {0}")]
  InvalidUserId(String),

  #[error("Async task pool error: {0}")]
  AsyncTaskPool(String),

  #[error("Push notification error: {0}")]
  PushNotification(String),

  #[error("Redis error: {0}")]
  RedisError(String),

  #[error("MQ Deserialization error: {0}")]
  MqDeserialization(String),
}

/// Result type for friendship operations
pub type FriendshipResult<T> = Result<T, FriendshipError>;
