//! Secure error handling to prevent information leakage
//!
//! This module provides utilities for handling errors securely without
//! exposing sensitive internal information to external clients.

use {crate::errors::FriendshipError,
     tracing::error};

/// Secure error handler that sanitizes errors before exposing them
pub struct SecureErrorHandler;

impl SecureErrorHandler {
  /// Sanitize database errors to prevent information leakage
  ///
  /// This function logs the full error internally but returns a sanitized
  /// version that doesn't expose sensitive database information.
  pub fn sanitize_database_error(error: &sqlx::Error, operation: &str) -> FriendshipError {
    // Log the full error for internal debugging
    error!("Database error during {}: {:?}", operation, error);

    // Return sanitized error based on error type
    match error {
      sqlx::Error::RowNotFound => FriendshipError::DatabaseError("Record not found".to_string()),
      sqlx::Error::Database(db_err) => {
        // Log database-specific error details internally
        error!("Database constraint error: {}", db_err);
        
        // Check for common constraint violations
        if db_err.message().contains("Duplicate entry") {
          FriendshipError::DatabaseError("Duplicate record".to_string())
        } else if db_err.message().contains("foreign key constraint") {
          FriendshipError::DatabaseError("Invalid reference".to_string())
        } else {
          FriendshipError::DatabaseError("Database operation failed".to_string())
        }
      }
      sqlx::Error::PoolTimedOut => {
        FriendshipError::DatabaseError("Service temporarily unavailable".to_string())
      }
      sqlx::Error::PoolClosed => {
        FriendshipError::DatabaseError("Service unavailable".to_string())
      }
      _ => FriendshipError::DatabaseError("Database operation failed".to_string()),
    }
  }

  /// Sanitize Redis errors to prevent information leakage
  pub fn sanitize_redis_error(error: &str, operation: &str) -> FriendshipError {
    // Log the full error for internal debugging
    error!("Redis error during {}: {}", operation, error);

    // Return sanitized error based on error message patterns
    if error.contains("timeout") || error.contains("Timeout") {
      FriendshipError::RedisError("Cache operation timed out".to_string())
    } else if error.contains("connection") || error.contains("Connection") {
      FriendshipError::RedisError("Cache service unavailable".to_string())
    } else if error.contains("auth") || error.contains("Auth") {
      FriendshipError::RedisError("Cache authentication failed".to_string())
    } else {
      FriendshipError::RedisError("Cache operation failed".to_string())
    }
  }

  /// Sanitize external service errors
  pub fn sanitize_external_service_error(error: &dyn std::error::Error, service_name: &str) -> FriendshipError {
    // Log the full error for internal debugging
    error!("External service error from {}: {:?}", service_name, error);

    // Return generic error without exposing service details
    FriendshipError::Downstream(format!("External service {} unavailable", service_name))
  }

  /// Sanitize validation errors (these are usually safe to expose)
  pub fn sanitize_validation_error(error: &str) -> FriendshipError {
    // Validation errors are usually safe to expose as they don't contain
    // sensitive system information
    FriendshipError::InvalidInput(error.to_string())
  }

  /// Check if an error should be logged at error level vs warn level
  pub fn should_log_as_error(error: &FriendshipError) -> bool {
    match error {
      FriendshipError::DatabaseError(_) => true,
      FriendshipError::RedisError(_) => true,
      FriendshipError::Downstream(_) => true,
      FriendshipError::AsyncTaskPool(_) => true,
      FriendshipError::InvalidInput(_) => false, // User input errors are warnings
      FriendshipError::InvalidUserId(_) => false,
      _ => true,
    }
  }

  /// Create a user-friendly error message for API responses
  pub fn create_user_friendly_message(error: &FriendshipError) -> String {
    match error {
      FriendshipError::DatabaseError(_) => "A database error occurred. Please try again later.".to_string(),
      FriendshipError::RedisError(_) => "A caching error occurred. Please try again later.".to_string(),
      FriendshipError::Downstream(_) => "An external service is unavailable. Please try again later.".to_string(),
      FriendshipError::InvalidInput(msg) => format!("Invalid input: {}", msg),
      FriendshipError::InvalidUserId(msg) => format!("Invalid user ID: {}", msg),
      FriendshipError::AsyncTaskPool(_) => "Task processing error. Please try again later.".to_string(),
      FriendshipError::PushNotification(_) => "Notification delivery failed. Please try again later.".to_string(),
      FriendshipError::MqDeserialization(_) => "Message processing error. Please try again later.".to_string(),
      _ => "An unexpected error occurred. Please try again later.".to_string(),
    }
  }
}

/// Macro for secure error logging and conversion
#[macro_export]
macro_rules! secure_db_error {
  ($error:expr, $operation:expr) => {
    crate::errors::secure_error::SecureErrorHandler::sanitize_database_error(&$error, $operation)
  };
}

/// Macro for secure Redis error logging and conversion
#[macro_export]
macro_rules! secure_redis_error {
  ($error:expr, $operation:expr) => {
    crate::errors::secure_error::SecureErrorHandler::sanitize_redis_error(&$error.to_string(), $operation)
  };
}

/// Macro for secure external service error logging and conversion
#[macro_export]
macro_rules! secure_external_error {
  ($error:expr, $service:expr) => {
    crate::errors::secure_error::SecureErrorHandler::sanitize_external_service_error(&$error, $service)
  };
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_sanitize_validation_error() {
    let error = SecureErrorHandler::sanitize_validation_error("Invalid phone number");
    match error {
      FriendshipError::InvalidInput(msg) => assert_eq!(msg, "Invalid phone number"),
      _ => panic!("Expected InvalidInput error"),
    }
  }

  #[test]
  fn test_should_log_as_error() {
    assert!(SecureErrorHandler::should_log_as_error(&FriendshipError::DatabaseError("test".to_string())));
    assert!(!SecureErrorHandler::should_log_as_error(&FriendshipError::InvalidInput("test".to_string())));
  }

  #[test]
  fn test_create_user_friendly_message() {
    let db_error = FriendshipError::DatabaseError("Internal DB error".to_string());
    let message = SecureErrorHandler::create_user_friendly_message(&db_error);
    assert_eq!(message, "A database error occurred. Please try again later.");
    
    let input_error = FriendshipError::InvalidInput("Bad phone number".to_string());
    let message = SecureErrorHandler::create_user_friendly_message(&input_error);
    assert_eq!(message, "Invalid input: Bad phone number");
  }
}
