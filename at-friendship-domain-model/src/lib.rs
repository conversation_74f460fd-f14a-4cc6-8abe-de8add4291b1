//! AT-Friendship Domain Model
//!
//! Domain model for friendship management service, including:
//! - Application layer interfaces and implementations
//! - Domain entities and value objects
//! - Repository interfaces
//! - Error handling

pub mod application;
pub mod entity;
pub mod errors;
pub mod repo;
pub mod service;
pub mod value;

pub mod all {
  use super::*;

  // Re-exports of commonly used types and functions
  pub use {application::{block_user::*,
                         botim_user::*,
                         contact::*},
           entity::{block_user::*,
                    botim_user::*,
                    contact::*,
                    notification::*},
           errors::*,
           repo::{auid::*,
                  block_user::*,
                  botim_user::*,
                  contact::*,
                  notification::*,
                  task_mgmt::*},
           service::{async_task_pool::factory::*,
                     blocked_user::factory::*,
                     botim_user::factory::*,
                     contact::factory::*,
                     notification::factory::*},
           value::{block_user::*,
                   botim_user::*,
                   contact::*}};
}
