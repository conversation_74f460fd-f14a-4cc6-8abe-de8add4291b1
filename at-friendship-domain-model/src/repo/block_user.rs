use at_deps::at_friendship::*;

use async_trait::async_trait;

use crate::all::*;

#[async_trait]
pub trait BlockedUserRepo: Send + Sync + 'static {
  async fn save_blocked_user_entity(&self, blocked_user_entity: &BlockedUserEntity) -> FriendshipResult<()>;
  async fn get_blocked_user_entities(&self, user_id: &BotimUserId) -> FriendshipResult<Vec<BlockedUserEntity>>;
  async fn is_blocked(&self, user_id: &BotimUserId, blocked_user_id: &BotimUserId) -> FriendshipResult<bool>;
  async fn is_blocked_by_users(
    &self,
    user_ids: &[BotimUserId],
    blocked_user_id: &BotimUserId,
  ) -> FriendshipResult<Vec<BotimUserId>>;
}
