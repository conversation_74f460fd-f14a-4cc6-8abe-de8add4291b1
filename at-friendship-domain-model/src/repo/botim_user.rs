//! Botim User Repository
use {crate::all::*,
     async_trait::async_trait,
     at_deps::at_friendship::*};

#[async_trait]
pub trait BotimUserRepo: Send + Sync + 'static {
  async fn save_botim_user_entities_idempotently(
    &self,
    botim_user_entities: &[BotimUserEntity],
  ) -> FriendshipResult<()>;

  async fn get_auids_by_phone_numbers(
    &self,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<BotimUserEntity>>;

  async fn get_botim_user_entities_by_auids(&self, auids: &[BotimUserId]) -> FriendshipResult<Vec<BotimUserEntity>>;

  async fn delete_botim_user(&self, botim_user_id: &BotimUserId) -> FriendshipResult<()>;
}
