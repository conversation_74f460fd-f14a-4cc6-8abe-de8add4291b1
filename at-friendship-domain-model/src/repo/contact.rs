use at_deps::at_friendship::*;

use async_trait::async_trait;

use crate::all::*;

#[async_trait]
pub trait ContactRepo: Send + Sync + 'static {
  /// Update contacts for owner
  async fn update_contacts_for_owner_idempotentially(
    &self,
    owner: &ContactOwnerId,
    contacts: &[ContactEntity],
  ) -> FriendshipResult<()>;

  async fn send_save_uid_notification(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<()>;

  async fn get_contact_owners_with_friend_phone_number(
    &self,
    phone_number: &PhoneNumber,
  ) -> FriendshipResult<Vec<ContactOwnerId>>;

  async fn get_contact_owners_with_friend_phone_number_and_owner_ids(
    &self,
    phone_number: &PhoneNumber,
    owner_ids: &[ContactOwnerId],
  ) -> FriendshipResult<Vec<ContactOwnerId>>;

  /// Get phone numbers that exist in the owner's contacts
  async fn get_existing_contact_phone_numbers(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<PhoneNumber>>;
}
