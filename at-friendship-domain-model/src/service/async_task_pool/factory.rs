use {at_deps::at_friendship::*,
     chrono::{self,
              Utc},
     std::{collections::HashMap,
           fmt,
           future::Future,
           pin::Pin,
           sync::{Arc,
                  Mutex,
                  Once,
                  OnceLock}},
     tokio::{sync::mpsc,
             task::JoinSet},
     tracing::{debug,
               error,
               warn},
     uuid::Uuid};

/// Global task pool instance
static GLOBAL_TASK_POOL: OnceLock<AsyncTaskPool<()>> = OnceLock::new();
static INIT: Once = Once::new();

/// Get or initialize the global task pool with environment-based configuration
pub fn global_task_pool() -> &'static AsyncTaskPool<()> {
  INIT.call_once(|| {
    let config = create_optimized_config();
    let _ = GLOBAL_TASK_POOL.set(AsyncTaskPool::new(config));
  });

  GLOBAL_TASK_POOL.get().expect("Global task pool not initialized")
}

/// Create optimized configuration based on environment
fn create_optimized_config() -> AsyncTaskPoolConfig {
  match std::env::var("ENVIRONMENT").as_deref() {
    Ok("production") | Ok("prod") => AsyncTaskPoolConfig {
      queue_capacity:       1000,
      max_concurrent_tasks: 50,
      keep_task_history:    false, // Disable in production for memory efficiency
      max_history_size:     0,
    },
    Ok("development") | Ok("dev") => AsyncTaskPoolConfig {
      queue_capacity:       200,
      max_concurrent_tasks: 20,
      keep_task_history:    true,
      max_history_size:     50,
    },
    Ok("test") | Ok("testing") => AsyncTaskPoolConfig {
      queue_capacity:       50,
      max_concurrent_tasks: 5,
      keep_task_history:    true,
      max_history_size:     20,
    },
    _ => AsyncTaskPoolConfig::default(),
  }
}

/// Represents an async task identifier
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct TaskId(String);

impl TaskId {
  fn new() -> Self {
    Self(Uuid::new_v4().to_string())
  }
}

impl fmt::Display for TaskId {
  fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
    write!(f, "{}", self.0)
  }
}

/// Task status
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum TaskStatus {
  Queued,
  Running,
  Completed,
  Failed(String),
}

/// Task information
#[derive(Debug, Clone)]
pub struct TaskInfo {
  pub id:              TaskId,
  pub status:          TaskStatus,
  pub creation_time:   chrono::DateTime<chrono::Utc>,
  pub completion_time: Option<chrono::DateTime<chrono::Utc>>,
}

/// Represents an executable async task
pub type AsyncTaskFn<T> = Box<dyn FnOnce() -> Pin<Box<dyn Future<Output = Result<T, String>> + Send>> + Send>;

/// Async task pool configuration
#[derive(Debug, Clone)]
pub struct AsyncTaskPoolConfig {
  /// Task queue capacity, tasks exceeding this capacity will be rejected
  pub queue_capacity:       usize,
  /// Maximum number of concurrent tasks
  pub max_concurrent_tasks: usize,
  /// Whether to keep detailed task history
  pub keep_task_history:    bool,
  /// Maximum history size
  pub max_history_size:     usize,
}

impl Default for AsyncTaskPoolConfig {
  fn default() -> Self {
    Self {
      queue_capacity:       1000,
      max_concurrent_tasks: 10,
      keep_task_history:    true,
      max_history_size:     100,
    }
  }
}

/// Generic async task execution pool
///
/// Can submit async tasks and get task status, supports concurrency limits and queue buffering
pub struct AsyncTaskPool<T: Send + 'static> {
  sender:        mpsc::Sender<(TaskId, AsyncTaskFn<T>)>,
  task_statuses: Arc<Mutex<HashMap<TaskId, TaskInfo>>>,
  config:        AsyncTaskPoolConfig,
}

impl<T: Send + 'static> Clone for AsyncTaskPool<T> {
  fn clone(&self) -> Self {
    Self {
      sender:        self.sender.clone(),
      task_statuses: Arc::clone(&self.task_statuses),
      config:        self.config.clone(),
    }
  }
}

impl<T: Send + 'static> AsyncTaskPool<T> {
  /// Create a new async task pool
  pub fn new(config: AsyncTaskPoolConfig) -> Self {
    let (sender, receiver) = mpsc::channel(config.queue_capacity);
    let task_statuses = Arc::new(Mutex::new(HashMap::new()));

    let worker = AsyncTaskWorker::new(
      receiver,
      Arc::clone(&task_statuses),
      config.max_concurrent_tasks,
      config.keep_task_history,
      config.max_history_size,
    );

    // Start the worker
    tokio::spawn(async move {
      worker.run().await;
    });

    Self {
      sender,
      task_statuses,
      config,
    }
  }

  /// Submit an async task
  pub async fn submit<F, Fut>(&self, task_fn: F) -> Result<TaskId, String>
  where
    F: FnOnce() -> Fut + Send + 'static,
    Fut: Future<Output = Result<T, String>> + Send + 'static, {
    let task_id = TaskId::new();

    // Create task info
    let task_info = TaskInfo {
      id:              task_id.clone(),
      status:          TaskStatus::Queued,
      creation_time:   Utc::now(),
      completion_time: None,
    };

    // Update task status
    match self.task_statuses.lock() {
      Ok(mut statuses) => {
        statuses.insert(task_id.clone(), task_info);
      }
      Err(e) => {
        let error_msg = format!("Failed to lock task_statuses: {e}");
        error!("{}", error_msg);
        return Err(error_msg);
      }
    }

    // Wrap task as BoxFuture
    let boxed_task: AsyncTaskFn<T> = Box::new(move || {
      let fut = task_fn();
      Box::pin(fut)
    });

    // Try to send task
    if let Err(e) = self.sender.send((task_id.clone(), boxed_task)).await {
      // Send failed, update status to Failed
      if let Ok(mut statuses) = self.task_statuses.lock() {
        if let Some(info) = statuses.get_mut(&task_id) {
          info.status = TaskStatus::Failed("Failed to queue task: channel closed".to_string());
          info.completion_time = Some(Utc::now());
        }
      } else {
        error!("Failed to lock task_statuses after send failure");
      }
      return Err(format!("Failed to submit task: {e}"));
    }
    Ok(task_id)
  }

  /// Get task status
  pub fn get_task_status(&self, task_id: &TaskId) -> Option<TaskInfo> {
    match self.task_statuses.lock() {
      Ok(statuses) => statuses.get(task_id).cloned(),
      Err(e) => {
        error!("Failed to lock task_statuses in get_task_status: {}", e);
        None
      }
    }
  }

  /// Get all task statuses
  pub fn get_all_task_statuses(&self) -> Vec<TaskInfo> {
    match self.task_statuses.lock() {
      Ok(statuses) => statuses.values().cloned().collect(),
      Err(e) => {
        error!("Failed to lock task_statuses in get_all_task_statuses: {}", e);
        Vec::new()
      }
    }
  }

  /// Clear completed task records
  pub fn clear_completed_tasks(&self) -> usize {
    match self.task_statuses.lock() {
      Ok(mut statuses) => {
        let before_count = statuses.len();

        statuses.retain(|_, info| info.status != TaskStatus::Completed);

        before_count - statuses.len()
      }
      Err(e) => {
        error!("Failed to lock task_statuses in clear_completed_tasks: {}", e);
        0
      }
    }
  }
}

/// Async task worker
struct AsyncTaskWorker<T: Send + 'static> {
  receiver:             mpsc::Receiver<(TaskId, AsyncTaskFn<T>)>,
  task_statuses:        Arc<Mutex<HashMap<TaskId, TaskInfo>>>,
  active_tasks:         JoinSet<(TaskId, Result<T, String>)>,
  max_concurrent_tasks: usize,
  keep_history:         bool,
  max_history_size:     usize,
}

impl<T: Send + 'static> AsyncTaskWorker<T> {
  fn new(
    receiver: mpsc::Receiver<(TaskId, AsyncTaskFn<T>)>,
    task_statuses: Arc<Mutex<HashMap<TaskId, TaskInfo>>>,
    max_concurrent_tasks: usize,
    keep_history: bool,
    max_history_size: usize,
  ) -> Self {
    Self {
      receiver,
      task_statuses,
      active_tasks: JoinSet::new(),
      max_concurrent_tasks,
      keep_history,
      max_history_size,
    }
  }

  async fn run(mut self) {
    debug!(
      "AsyncTaskWorker started with max {} concurrent tasks",
      self.max_concurrent_tasks
    );

    loop {
      tokio::select! {
          // Handle completed tasks
          Some(result) = self.active_tasks.join_next(), if !self.active_tasks.is_empty() => {
              match result {
                  Ok((task_id, task_result)) => {
                      self.update_task_status(task_id, task_result);
                  },
                  Err(e) => {
                      error!("Task join error: {}", e);
                  }
              }

              // Manage history size
              self.prune_history_if_needed();
          },

          // Receive new tasks
          Some((task_id, task_fn)) = self.receiver.recv() => {
              // Wait for available slot
              while self.active_tasks.len() >= self.max_concurrent_tasks {
                  match self.active_tasks.join_next().await {
                      Some(Ok((completed_task_id, task_result))) => {
                          self.update_task_status(completed_task_id, task_result);
                      },
                      Some(Err(e)) => {
                          error!("Task join error while waiting for slot: {}", e);
                      },
                      None => break,
                  }
              }

              // Update task status to running
              if let Ok(mut statuses) = self.task_statuses.lock() {
                  if let Some(info) = statuses.get_mut(&task_id) {
                      info.status = TaskStatus::Running;
                  }
              } else {
                  error!("Failed to lock task_statuses when updating task to running state");
              }

              debug!("Starting task {}", task_id);
              let task_id_clone = task_id.clone();

              // Submit task to coroutine pool
              self.active_tasks.spawn(async move {
                  let result = task_fn().await;
                  (task_id_clone, result)
              });
          },

          // Channel closed, process remaining tasks
          else => {
              debug!("Task channel closed, waiting for {} active tasks to complete", self.active_tasks.len());

              while let Some(result) = self.active_tasks.join_next().await {
                  match result {
                      Ok((task_id, task_result)) => {
                          self.update_task_status(task_id, task_result);
                      },
                      Err(e) => {
                          error!("Task join error during shutdown: {}", e);
                      }
                  }
              }

              debug!("AsyncTaskWorker stopped");
              break;
          }
      }
    }
  }

  // Update task status
  fn update_task_status(&self, task_id: TaskId, result: Result<T, String>) {
    match self.task_statuses.lock() {
      Ok(mut statuses) => {
        if let Some(info) = statuses.get_mut(&task_id) {
          match result {
            Ok(_) => {
              info.status = TaskStatus::Completed;
              debug!("Task {} completed successfully", task_id);
            }
            Err(e) => {
              info.status = TaskStatus::Failed(e.clone());
              warn!("Task {} failed: {}", task_id, e);
            }
          }

          info.completion_time = Some(Utc::now());
        } else {
          error!("Received result for unknown task: {}", task_id);
        }
      }
      Err(e) => {
        error!(
          "Failed to lock task_statuses in update_task_status: {} for task {}",
          e, task_id
        );
      }
    }
  }

  // Prune excessive history records
  fn prune_history_if_needed(&self) {
    if !self.keep_history {
      return;
    }

    let statuses_result = self.task_statuses.lock();
    if statuses_result.is_err() {
      error!(
        "Failed to lock task_statuses in prune_history_if_needed: {}",
        statuses_result
          .err()
          .map_or_else(|| "unknown error".to_string(), |e| e.to_string())
      );
      return;
    }

    let mut statuses = match statuses_result {
      Ok(guard) => guard,
      Err(_) => return,
    };

    // If history is below threshold, do nothing
    if statuses.len() <= self.max_history_size {
      return;
    }

    // Collect completed tasks and sort by completion time
    let mut completed_tasks: Vec<_> = statuses
      .iter()
      .filter(|(_, info)| matches!(info.status, TaskStatus::Completed | TaskStatus::Failed(_)))
      .map(|(id, info)| (id.clone(), info.completion_time.unwrap_or_else(Utc::now)))
      .collect();

    // Sort by time (earliest first)
    completed_tasks.sort_by(|a, b| a.1.cmp(&b.1));

    // Calculate how many records to remove
    let to_remove = statuses.len() - self.max_history_size;

    // Remove oldest records
    for item in completed_tasks.iter().take(to_remove) {
      statuses.remove(&item.0);
    }

    if to_remove > 0 {
      debug!("Pruned {} old task records", to_remove.min(completed_tasks.len()));
    }
  }
}

#[cfg(test)]
mod tests {
  use {super::*,
       tokio::time::{Duration,
                     sleep}};

  #[tokio::test]
  async fn test_global_task_pool() {
    // Get the global task pool
    let pool = global_task_pool();

    // Submit a task and wait for execution
    let task_id = pool
      .submit(|| async {
        sleep(Duration::from_millis(50)).await;
        Ok(())
      })
      .await
      .expect("Failed to submit task to global pool");

    // Wait for task completion
    sleep(Duration::from_millis(100)).await;

    // Verify task status
    let status = pool.get_task_status(&task_id).expect("Task status not found");
    assert_eq!(status.status, TaskStatus::Completed, "Task should be completed");

    // Verify the global pool is truly a singleton
    let pool2 = global_task_pool();
    assert!(
      std::ptr::eq(pool, pool2),
      "Global task pool should return the same instance"
    );
  }

  #[tokio::test]
  async fn test_task_submission_and_execution() {
    let pool = AsyncTaskPool::<String>::new(AsyncTaskPoolConfig {
      queue_capacity: 100,
      max_concurrent_tasks: 5,
      ..Default::default()
    });

    // Submit a successful task
    let task_id = pool
      .submit(|| async {
        sleep(Duration::from_millis(100)).await;
        Ok("task completed".to_string())
      })
      .await
      .unwrap();

    // Wait for task completion
    sleep(Duration::from_millis(200)).await;

    // Check task status
    if let Some(status) = pool.get_task_status(&task_id) {
      assert_eq!(status.status, TaskStatus::Completed);
    } else {
      panic!("Task status not found");
    }

    // Submit a failing task
    let error_task_id = pool
      .submit(|| async {
        sleep(Duration::from_millis(50)).await;
        Err("task failed".to_string())
      })
      .await
      .unwrap();

    // Wait for task completion
    sleep(Duration::from_millis(100)).await;

    // Check task status
    if let Some(error_status) = pool.get_task_status(&error_task_id) {
      assert!(matches!(error_status.status, TaskStatus::Failed(_)));
    } else {
      panic!("Task status not found");
    }
  }

  #[tokio::test]
  async fn test_concurrent_execution_limit() {
    let pool = AsyncTaskPool::<()>::new(AsyncTaskPoolConfig {
      queue_capacity: 20,
      max_concurrent_tasks: 2,
      ..Default::default()
    });

    // Create mutexes to track concurrent running tasks
    let counter = Arc::new(Mutex::new(0));
    let max_concurrent = Arc::new(Mutex::new(0));

    // Submit 10 tasks
    let mut task_ids = Vec::new();
    for _ in 0 .. 10 {
      let counter_clone = Arc::clone(&counter);
      let max_concurrent_clone = Arc::clone(&max_concurrent);

      let task_id = pool
        .submit(move || async move {
          // Increment counter
          if let Ok(mut count) = counter_clone.lock() {
            *count += 1;

            // Update max concurrent count
            if let Ok(mut max) = max_concurrent_clone.lock() {
              if *count > *max {
                *max = *count;
              }
            } else {
              return Err("Failed to lock max_concurrent".to_string());
            }
          } else {
            return Err("Failed to lock counter".to_string());
          }

          // Task sleep
          sleep(Duration::from_millis(100)).await;

          // Decrement counter
          if let Ok(mut count) = counter_clone.lock() {
            *count -= 1;
          } else {
            return Err("Failed to lock counter for decrement".to_string());
          }

          Ok(())
        })
        .await
        .unwrap();

      task_ids.push(task_id);
    }

    // Wait for all tasks to complete
    sleep(Duration::from_millis(600)).await;

    // Check if max concurrent is 2
    match max_concurrent.lock() {
      Ok(max) => {
        assert_eq!(*max, 2, "Maximum concurrent tasks should be 2");
      }
      Err(e) => {
        panic!("Failed to lock max_concurrent: {e}");
      }
    }

    // Verify all tasks completed
    for task_id in task_ids {
      if let Some(status) = pool.get_task_status(&task_id) {
        assert_eq!(status.status, TaskStatus::Completed);
      } else {
        panic!("Task status not found");
      }
    }
  }
}
