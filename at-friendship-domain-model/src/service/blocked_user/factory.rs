use {crate::{all::*,
             entity::block_user::BlockedUserEntity},
     at_deps::at_friendship::*,
     at_internal_deps::at_friendship::*};

use at_lib_comm_domain::value::time::{CreateAt,
                                      UpdateAt};

pub struct BlockedUserFactory;

impl BlockedUserFactory {
  #[tracing::instrument(name = "create_blocked_user_entity", skip(user_id, blocked_user_id, status))]
  pub fn create_blocked_user_entity(
    user_id: &BotimUserId,
    blocked_user_id: &BotimUserId,
    status: BlockUserStatus,
  ) -> BlockedUserEntity {
    BlockedUserEntity {
      user_id: user_id.clone(),
      blocked_user_id: blocked_user_id.clone(),
      status,
      created_at: CreateAt::now(),
      updated_at: UpdateAt::now(),
    }
  }
}
