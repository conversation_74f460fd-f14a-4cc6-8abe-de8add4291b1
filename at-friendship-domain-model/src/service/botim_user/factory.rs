use std::collections::HashMap;

use {crate::{all::*,
             entity::botim_user::BotimUserEntity},
     at_deps::at_friendship::*,
     at_internal_deps::at_friendship::*};

use at_lib_comm_domain::value::time::{CreateAt,
                                      UpdateAt};
pub struct BotimUserServiceFactory;

impl BotimUserServiceFactory {
  /// Create BotimUserEntity from (PhoneNumber, Auid) units
  #[tracing::instrument(name = "create_botim_user_entities", skip(auids))]
  pub fn create_botim_user_entities(
    auids: Vec<(PhoneNumber, Auid, AuidRepr)>,
  ) -> FriendshipResult<Vec<BotimUserEntity>> {
    let botim_user_entities = auids
      .into_iter()
      .map(|(phone_number, auid, auid_repr)| BotimUserEntity {
        botim_user_id: BotimUserId::new(auid.clone()),
        auid_repr,
        phone_number,
        created_at: CreateAt::now(),
        updated_at: UpdateAt::now(),
      })
      .collect();

    Ok(botim_user_entities)
  }

  // convert contact owner vector to auid vector
  #[tracing::instrument(name = "convert_contact_owners_to_botim_user_ids", skip(contact_owners))]
  pub fn convert_contact_owners_to_botim_user_ids(
    contact_owners: Vec<ContactOwnerId>,
  ) -> FriendshipResult<Vec<BotimUserId>> {
    let botim_user_ids = contact_owners
      .into_iter()
      .map(|c| BotimUserId::new(c.inner_ref().clone()))
      .collect();
    Ok(botim_user_ids)
  }

  // get botim user repr vector from botim user entity vector
  #[tracing::instrument(name = "get_botim_user_repr_vector", skip(botim_user_entities))]
  pub fn get_botim_user_repr_vector(botim_user_entities: Vec<BotimUserEntity>) -> FriendshipResult<Vec<AuidRepr>> {
    let botim_user_reprs = botim_user_entities.into_iter().map(|b| b.auid_repr.clone()).collect();
    Ok(botim_user_reprs)
  }

  // get phone number vector from botim user entity vector
  #[tracing::instrument(name = "get_phone_number_vector", skip(botim_user_entities))]
  pub fn get_phone_number_vector(
    botim_user_entities: Vec<BotimUserEntity>,
  ) -> FriendshipResult<HashMap<PhoneNumber, BotimUserId>> {
    let phone_numbers = botim_user_entities
      .into_iter()
      .map(|b| (b.phone_number.clone(), b.botim_user_id.clone()))
      .collect();
    Ok(phone_numbers)
  }

  #[tracing::instrument(name = "check_save_botim_user_entities_param_validity", skip(botim_user_entities))]
  pub fn check_save_botim_user_entities_param_validity(
    botim_user_entities: &[BotimUserEntity],
  ) -> FriendshipResult<()> {
    if botim_user_entities.is_empty() {
      return Err(FriendshipError::InvalidInput(
        "Botim user entities is empty".to_string(),
      ));
    }

    // check botim_user_entities size is not greater than 100
    if botim_user_entities.len() > 100 {
      return Err(FriendshipError::InvalidInput(
        "Botim user entities size is greater than 100".to_string(),
      ));
    }
    Ok(())
  }
}
