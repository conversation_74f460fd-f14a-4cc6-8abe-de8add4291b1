use {crate::all::*,
     at_deps::at_friendship::*,
     at_internal_deps::at_friendship::{at_lib_comm_domain::id::uuid::UuidBytes,
                                       *},
     at_lib_comm_domain::value::time::{CreateAt,
                                       UpdateAt},
     std::collections::HashMap};

pub struct ContactFactory;

impl ContactFactory {
  #[tracing::instrument(name = "create_contact_entities", skip(contact_operation))]
  pub fn create_contact_entities(contact_operation: &ContactOperation) -> FriendshipResult<Vec<ContactEntity>> {
    let entities = contact_operation
      .add
      .iter()
      .map(|info| ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     contact_operation.owner_id.clone(),
        contact_name: info.name.clone(),
        phone_number: info.phone_number.clone(),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::now(),
        updated_at:   UpdateAt::now(),
      })
      .chain(contact_operation.delete.iter().map(|info| ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     contact_operation.owner_id.clone(),
        contact_name: info.name.clone(),
        phone_number: info.phone_number.clone(),
        status:       ContactStatus::Deleted,
        created_at:   CreateAt::now(),
        updated_at:   UpdateAt::now(),
      }))
      .chain(contact_operation.update.iter().map(|info| ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     contact_operation.owner_id.clone(),
        contact_name: info.name.clone(),
        phone_number: info.phone_number.clone(),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::now(),
        updated_at:   UpdateAt::now(),
      }))
      .collect();

    Ok(entities)
  }

  #[tracing::instrument(name = "get_phone_number_filters", skip(contact_entities))]
  pub fn get_phone_number_filters(contact_entities: &[ContactEntity]) -> Vec<PhoneNumber> {
    contact_entities
      .iter()
      .filter(|info| info.status == ContactStatus::Normal)
      .map(|info| info.phone_number.clone())
      .collect()
  }

  #[tracing::instrument(
    name = "filter_botim_user_ids_by_contact_phone_numbers",
    skip(phone_numbers, contact_phone_numbers)
  )]
  pub fn filter_botim_user_ids_by_contact_phone_numbers(
    phone_numbers: &HashMap<PhoneNumber, BotimUserId>,
    contact_phone_numbers: &[PhoneNumber],
  ) -> Vec<BotimUserId> {
    phone_numbers
      .iter()
      .filter(|(phone_number, _)| contact_phone_numbers.contains(phone_number))
      .map(|(_, botim_user_id)| botim_user_id.clone())
      .collect()
  }

  #[tracing::instrument(name = "check_param_validity", skip(contact_operation))]
  pub fn check_param_validity(contact_operation: &ContactOperation) -> FriendshipResult<()> {
    if contact_operation.add.is_empty() && contact_operation.delete.is_empty() && contact_operation.update.is_empty() {
      return Err(FriendshipError::InvalidInput(
        "No contact operation provided".to_string(),
      ));
    }
    // check contact_operation.add + contact_operation.delete + contact_operation.update size is not greater than 100
    if contact_operation.add.len() + contact_operation.delete.len() + contact_operation.update.len() > 100 {
      return Err(FriendshipError::InvalidInput(
        "Contact operation size is greater than 100".to_string(),
      ));
    }
    Ok(())
  }

  #[tracing::instrument(name = "check_get_existing_contact_owners_param_validity", skip(owner_ids))]
  pub fn check_get_existing_contact_owners_param_validity(owner_ids: &[ContactOwnerId]) -> FriendshipResult<()> {
    if owner_ids.is_empty() {
      return Err(FriendshipError::InvalidInput("Owner ids is empty".to_string()));
    }

    // check owner_ids size is not greater than 100
    if owner_ids.len() > 100 {
      return Err(FriendshipError::InvalidInput(
        "Owner ids size is greater than 100".to_string(),
      ));
    }
    Ok(())
  }
}

pub struct BotimUserFactory;

impl BotimUserFactory {}
