use {crate::all::*,
     at_deps::at_friendship::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::value::time::CreateAt};
pub struct NotificationFactory;

impl NotificationFactory {
  #[tracing::instrument(
    name = "create_register_notification_entity",
    skip(contact_owners, register_event, phone_number, auid_repr, botim_user_id)
  )]
  pub fn create_register_notification_entity(
    contact_owners: Vec<AuidRepr>,
    register_event: RegisterEvent,
    phone_number: PhoneNumber,
    auid_repr: AuidRepr,
    botim_user_id: BotimUserId,
  ) -> BotimUserNotificationEntity {
    BotimUserNotificationEntity {
      receiver:   contact_owners,
      status:     register_event,
      botim_user: Some(BotimUser {
        botim_user_id,
        auid_repr,
        phone_number,
      }),
      created_at: CreateAt::now(),
    }
  }

  #[tracing::instrument(
    name = "create_unregister_notification_entity",
    skip(contact_owners, register_event, phone_number, auid_repr, botim_user_id)
  )]
  pub fn create_unregister_notification_entity(
    contact_owners: Vec<AuidRepr>,
    register_event: RegisterEvent,
    phone_number: PhoneNumber,
    auid_repr: AuidRepr,
    botim_user_id: BotimUserId,
  ) -> BotimUserNotificationEntity {
    BotimUserNotificationEntity {
      receiver:   contact_owners,
      status:     register_event,
      botim_user: Some(BotimUser {
        botim_user_id,
        auid_repr,
        phone_number,
      }),
      created_at: CreateAt::now(),
    }
  }
}
