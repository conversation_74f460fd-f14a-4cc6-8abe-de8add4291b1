//! Input validation module
//!
//! This module provides comprehensive input validation for all user inputs
//! to prevent injection attacks and ensure data integrity.

pub mod phone_validation;

use crate::all::FriendshipError;

/// Common validation constants
pub struct ValidationConstants;

impl ValidationConstants {
  /// Maximum length for user names and contact names
  pub const MAX_NAME_LENGTH: usize = 100;
  
  /// Maximum length for AUID representation strings
  pub const MAX_AUID_REPR_LENGTH: usize = 50;
  
  /// Maximum number of contacts in a single operation
  pub const MAX_CONTACTS_PER_OPERATION: usize = 100;
  
  /// Maximum number of user IDs in a single query
  pub const MAX_USER_IDS_PER_QUERY: usize = 1000;
  
  /// Minimum phone number (7 digits)
  pub const MIN_PHONE_NUMBER: u64 = 1000000;
  
  /// Maximum phone number (15 digits)
  pub const MAX_PHONE_NUMBER: u64 = 999999999999999;
}

/// Base validator trait
pub trait Validator<T> {
  fn validate(&self, input: &T) -> Result<(), FriendshipError>;
}

/// String validation utilities
pub struct StringValidator;

impl StringValidator {
  /// Validate a string field with length and content checks
  pub fn validate_string(
    value: &str,
    field_name: &str,
    max_length: usize,
    allow_empty: bool,
  ) -> Result<(), FriendshipError> {
    if !allow_empty && value.is_empty() {
      return Err(FriendshipError::InvalidInput(format!(
        "{} cannot be empty",
        field_name
      )));
    }

    if value.len() > max_length {
      return Err(FriendshipError::InvalidInput(format!(
        "{} too long: {} characters (max {})",
        field_name,
        value.len(),
        max_length
      )));
    }

    // Check for null bytes
    if value.contains('\0') {
      return Err(FriendshipError::InvalidInput(format!(
        "{} contains invalid null character",
        field_name
      )));
    }

    // Check for control characters (except tab, newline, carriage return)
    if value.chars().any(|c| c.is_control() && c != '\t' && c != '\n' && c != '\r') {
      return Err(FriendshipError::InvalidInput(format!(
        "{} contains invalid control characters",
        field_name
      )));
    }

    Ok(())
  }

  /// Validate AUID representation string
  pub fn validate_auid_repr(auid_repr: &str) -> Result<(), FriendshipError> {
    Self::validate_string(auid_repr, "AUID representation", ValidationConstants::MAX_AUID_REPR_LENGTH, false)?;
    
    // Additional validation for AUID format (should be alphanumeric with hyphens)
    if !auid_repr.chars().all(|c| c.is_alphanumeric() || c == '-' || c == '_') {
      return Err(FriendshipError::InvalidInput(
        "AUID representation can only contain alphanumeric characters, hyphens, and underscores".to_string(),
      ));
    }

    Ok(())
  }

  /// Validate contact name
  pub fn validate_contact_name(name: &str) -> Result<(), FriendshipError> {
    Self::validate_string(name, "Contact name", ValidationConstants::MAX_NAME_LENGTH, false)?;
    
    // Additional validation for names (no leading/trailing whitespace)
    if name.trim() != name {
      return Err(FriendshipError::InvalidInput(
        "Contact name cannot have leading or trailing whitespace".to_string(),
      ));
    }

    Ok(())
  }
}

/// Collection validation utilities
pub struct CollectionValidator;

impl CollectionValidator {
  /// Validate collection size
  pub fn validate_size<T>(
    collection: &[T],
    field_name: &str,
    max_size: usize,
    allow_empty: bool,
  ) -> Result<(), FriendshipError> {
    if !allow_empty && collection.is_empty() {
      return Err(FriendshipError::InvalidInput(format!(
        "{} cannot be empty",
        field_name
      )));
    }

    if collection.len() > max_size {
      return Err(FriendshipError::InvalidInput(format!(
        "{} contains too many items: {} (max {})",
        field_name,
        collection.len(),
        max_size
      )));
    }

    Ok(())
  }

  /// Validate that a collection has no duplicates
  pub fn validate_no_duplicates<T>(
    collection: &[T],
    field_name: &str,
  ) -> Result<(), FriendshipError>
  where
    T: PartialEq,
  {
    for (i, item) in collection.iter().enumerate() {
      if collection[i + 1..].contains(item) {
        return Err(FriendshipError::InvalidInput(format!(
          "{} contains duplicate items",
          field_name
        )));
      }
    }
    Ok(())
  }
}

/// UUID validation utilities
pub struct UuidValidator;

impl UuidValidator {
  /// Validate UUID bytes
  pub fn validate_uuid_bytes(bytes: &[u8], field_name: &str) -> Result<(), FriendshipError> {
    if bytes.len() != 16 {
      return Err(FriendshipError::InvalidInput(format!(
        "{} must be exactly 16 bytes, got {}",
        field_name,
        bytes.len()
      )));
    }

    // Check if all bytes are zero (invalid UUID)
    if bytes.iter().all(|&b| b == 0) {
      return Err(FriendshipError::InvalidInput(format!(
        "{} cannot be all zeros",
        field_name
      )));
    }

    Ok(())
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_string_validation_success() {
    let result = StringValidator::validate_string("valid_string", "test_field", 20, false);
    assert!(result.is_ok());
  }

  #[test]
  fn test_string_validation_empty() {
    let result = StringValidator::validate_string("", "test_field", 20, false);
    assert!(result.is_err());
  }

  #[test]
  fn test_string_validation_too_long() {
    let result = StringValidator::validate_string("this_string_is_way_too_long", "test_field", 10, false);
    assert!(result.is_err());
  }

  #[test]
  fn test_string_validation_null_character() {
    let result = StringValidator::validate_string("test\0string", "test_field", 20, false);
    assert!(result.is_err());
  }

  #[test]
  fn test_auid_repr_validation_success() {
    let result = StringValidator::validate_auid_repr("user-123_abc");
    assert!(result.is_ok());
  }

  #[test]
  fn test_auid_repr_validation_invalid_chars() {
    let result = StringValidator::validate_auid_repr("user@123");
    assert!(result.is_err());
  }

  #[test]
  fn test_contact_name_validation_success() {
    let result = StringValidator::validate_contact_name("John Doe");
    assert!(result.is_ok());
  }

  #[test]
  fn test_contact_name_validation_whitespace() {
    let result = StringValidator::validate_contact_name(" John Doe ");
    assert!(result.is_err());
  }

  #[test]
  fn test_collection_size_validation_success() {
    let collection = vec![1, 2, 3];
    let result = CollectionValidator::validate_size(&collection, "test_collection", 5, false);
    assert!(result.is_ok());
  }

  #[test]
  fn test_collection_size_validation_empty() {
    let collection: Vec<i32> = vec![];
    let result = CollectionValidator::validate_size(&collection, "test_collection", 5, false);
    assert!(result.is_err());
  }

  #[test]
  fn test_collection_size_validation_too_large() {
    let collection = vec![1, 2, 3, 4, 5, 6];
    let result = CollectionValidator::validate_size(&collection, "test_collection", 5, false);
    assert!(result.is_err());
  }

  #[test]
  fn test_collection_no_duplicates_success() {
    let collection = vec![1, 2, 3, 4];
    let result = CollectionValidator::validate_no_duplicates(&collection, "test_collection");
    assert!(result.is_ok());
  }

  #[test]
  fn test_collection_no_duplicates_failure() {
    let collection = vec![1, 2, 3, 2];
    let result = CollectionValidator::validate_no_duplicates(&collection, "test_collection");
    assert!(result.is_err());
  }

  #[test]
  fn test_uuid_validation_success() {
    let uuid_bytes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];
    let result = UuidValidator::validate_uuid_bytes(&uuid_bytes, "test_uuid");
    assert!(result.is_ok());
  }

  #[test]
  fn test_uuid_validation_wrong_length() {
    let uuid_bytes = [1, 2, 3, 4, 5];
    let result = UuidValidator::validate_uuid_bytes(&uuid_bytes, "test_uuid");
    assert!(result.is_err());
  }

  #[test]
  fn test_uuid_validation_all_zeros() {
    let uuid_bytes = [0; 16];
    let result = UuidValidator::validate_uuid_bytes(&uuid_bytes, "test_uuid");
    assert!(result.is_err());
  }
}
