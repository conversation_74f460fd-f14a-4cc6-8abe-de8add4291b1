//! Phone number validation module
//!
//! This module provides comprehensive phone number validation including
//! format checking, country code validation, and security checks.

use {crate::{all::FriendshipError,
             validation::{ValidationConstants,
                          Validator}},
     std::collections::HashSet};

/// Phone number validator
pub struct PhoneNumberValidator {
  /// Set of valid country codes (first 1-3 digits)
  valid_country_codes: HashSet<u16>,
}

impl PhoneNumberValidator {
  /// Create a new phone number validator with default country codes
  pub fn new() -> Self {
    let mut valid_country_codes = HashSet::new();
    
    // Add common country codes
    valid_country_codes.insert(1);   // US/Canada
    valid_country_codes.insert(7);   // Russia/Kazakhstan
    valid_country_codes.insert(20);  // Egypt
    valid_country_codes.insert(27);  // South Africa
    valid_country_codes.insert(30);  // Greece
    valid_country_codes.insert(31);  // Netherlands
    valid_country_codes.insert(32);  // Belgium
    valid_country_codes.insert(33);  // France
    valid_country_codes.insert(34);  // Spain
    valid_country_codes.insert(36);  // Hungary
    valid_country_codes.insert(39);  // Italy
    valid_country_codes.insert(40);  // Romania
    valid_country_codes.insert(41);  // Switzerland
    valid_country_codes.insert(43);  // Austria
    valid_country_codes.insert(44);  // UK
    valid_country_codes.insert(45);  // Denmark
    valid_country_codes.insert(46);  // Sweden
    valid_country_codes.insert(47);  // Norway
    valid_country_codes.insert(48);  // Poland
    valid_country_codes.insert(49);  // Germany
    valid_country_codes.insert(51);  // Peru
    valid_country_codes.insert(52);  // Mexico
    valid_country_codes.insert(53);  // Cuba
    valid_country_codes.insert(54);  // Argentina
    valid_country_codes.insert(55);  // Brazil
    valid_country_codes.insert(56);  // Chile
    valid_country_codes.insert(57);  // Colombia
    valid_country_codes.insert(58);  // Venezuela
    valid_country_codes.insert(60);  // Malaysia
    valid_country_codes.insert(61);  // Australia
    valid_country_codes.insert(62);  // Indonesia
    valid_country_codes.insert(63);  // Philippines
    valid_country_codes.insert(64);  // New Zealand
    valid_country_codes.insert(65);  // Singapore
    valid_country_codes.insert(66);  // Thailand
    valid_country_codes.insert(81);  // Japan
    valid_country_codes.insert(82);  // South Korea
    valid_country_codes.insert(84);  // Vietnam
    valid_country_codes.insert(86);  // China
    valid_country_codes.insert(90);  // Turkey
    valid_country_codes.insert(91);  // India
    valid_country_codes.insert(92);  // Pakistan
    valid_country_codes.insert(93);  // Afghanistan
    valid_country_codes.insert(94);  // Sri Lanka
    valid_country_codes.insert(95);  // Myanmar
    valid_country_codes.insert(98);  // Iran
    valid_country_codes.insert(212); // Morocco
    valid_country_codes.insert(213); // Algeria
    valid_country_codes.insert(216); // Tunisia
    valid_country_codes.insert(218); // Libya
    valid_country_codes.insert(220); // Gambia
    valid_country_codes.insert(221); // Senegal
    valid_country_codes.insert(222); // Mauritania
    valid_country_codes.insert(223); // Mali
    valid_country_codes.insert(224); // Guinea
    valid_country_codes.insert(225); // Ivory Coast
    valid_country_codes.insert(226); // Burkina Faso
    valid_country_codes.insert(227); // Niger
    valid_country_codes.insert(228); // Togo
    valid_country_codes.insert(229); // Benin
    valid_country_codes.insert(230); // Mauritius
    valid_country_codes.insert(231); // Liberia
    valid_country_codes.insert(232); // Sierra Leone
    valid_country_codes.insert(233); // Ghana
    valid_country_codes.insert(234); // Nigeria
    valid_country_codes.insert(235); // Chad
    valid_country_codes.insert(236); // Central African Republic
    valid_country_codes.insert(237); // Cameroon
    valid_country_codes.insert(238); // Cape Verde
    valid_country_codes.insert(239); // São Tomé and Príncipe
    valid_country_codes.insert(240); // Equatorial Guinea
    valid_country_codes.insert(241); // Gabon
    valid_country_codes.insert(242); // Republic of the Congo
    valid_country_codes.insert(243); // Democratic Republic of the Congo
    valid_country_codes.insert(244); // Angola
    valid_country_codes.insert(245); // Guinea-Bissau
    valid_country_codes.insert(246); // British Indian Ocean Territory
    valid_country_codes.insert(248); // Seychelles
    valid_country_codes.insert(249); // Sudan
    valid_country_codes.insert(250); // Rwanda
    valid_country_codes.insert(251); // Ethiopia
    valid_country_codes.insert(252); // Somalia
    valid_country_codes.insert(253); // Djibouti
    valid_country_codes.insert(254); // Kenya
    valid_country_codes.insert(255); // Tanzania
    valid_country_codes.insert(256); // Uganda
    valid_country_codes.insert(257); // Burundi
    valid_country_codes.insert(258); // Mozambique
    valid_country_codes.insert(260); // Zambia
    valid_country_codes.insert(261); // Madagascar
    valid_country_codes.insert(262); // Réunion/Mayotte
    valid_country_codes.insert(263); // Zimbabwe
    valid_country_codes.insert(264); // Namibia
    valid_country_codes.insert(265); // Malawi
    valid_country_codes.insert(266); // Lesotho
    valid_country_codes.insert(267); // Botswana
    valid_country_codes.insert(268); // Eswatini
    valid_country_codes.insert(269); // Comoros
    valid_country_codes.insert(290); // Saint Helena
    valid_country_codes.insert(291); // Eritrea
    valid_country_codes.insert(297); // Aruba
    valid_country_codes.insert(298); // Faroe Islands
    valid_country_codes.insert(299); // Greenland
    valid_country_codes.insert(350); // Gibraltar
    valid_country_codes.insert(351); // Portugal
    valid_country_codes.insert(352); // Luxembourg
    valid_country_codes.insert(353); // Ireland
    valid_country_codes.insert(354); // Iceland
    valid_country_codes.insert(355); // Albania
    valid_country_codes.insert(356); // Malta
    valid_country_codes.insert(357); // Cyprus
    valid_country_codes.insert(358); // Finland
    valid_country_codes.insert(359); // Bulgaria
    valid_country_codes.insert(370); // Lithuania
    valid_country_codes.insert(371); // Latvia
    valid_country_codes.insert(372); // Estonia
    valid_country_codes.insert(373); // Moldova
    valid_country_codes.insert(374); // Armenia
    valid_country_codes.insert(375); // Belarus
    valid_country_codes.insert(376); // Andorra
    valid_country_codes.insert(377); // Monaco
    valid_country_codes.insert(378); // San Marino
    valid_country_codes.insert(380); // Ukraine
    valid_country_codes.insert(381); // Serbia
    valid_country_codes.insert(382); // Montenegro
    valid_country_codes.insert(383); // Kosovo
    valid_country_codes.insert(385); // Croatia
    valid_country_codes.insert(386); // Slovenia
    valid_country_codes.insert(387); // Bosnia and Herzegovina
    valid_country_codes.insert(389); // North Macedonia
    valid_country_codes.insert(420); // Czech Republic
    valid_country_codes.insert(421); // Slovakia
    valid_country_codes.insert(423); // Liechtenstein
    valid_country_codes.insert(500); // Falkland Islands
    valid_country_codes.insert(501); // Belize
    valid_country_codes.insert(502); // Guatemala
    valid_country_codes.insert(503); // El Salvador
    valid_country_codes.insert(504); // Honduras
    valid_country_codes.insert(505); // Nicaragua
    valid_country_codes.insert(506); // Costa Rica
    valid_country_codes.insert(507); // Panama
    valid_country_codes.insert(508); // Saint Pierre and Miquelon
    valid_country_codes.insert(509); // Haiti
    valid_country_codes.insert(590); // Guadeloupe
    valid_country_codes.insert(591); // Bolivia
    valid_country_codes.insert(592); // Guyana
    valid_country_codes.insert(593); // Ecuador
    valid_country_codes.insert(594); // French Guiana
    valid_country_codes.insert(595); // Paraguay
    valid_country_codes.insert(596); // Martinique
    valid_country_codes.insert(597); // Suriname
    valid_country_codes.insert(598); // Uruguay
    valid_country_codes.insert(599); // Netherlands Antilles
    valid_country_codes.insert(670); // East Timor
    valid_country_codes.insert(672); // Australian External Territories
    valid_country_codes.insert(673); // Brunei
    valid_country_codes.insert(674); // Nauru
    valid_country_codes.insert(675); // Papua New Guinea
    valid_country_codes.insert(676); // Tonga
    valid_country_codes.insert(677); // Solomon Islands
    valid_country_codes.insert(678); // Vanuatu
    valid_country_codes.insert(679); // Fiji
    valid_country_codes.insert(680); // Palau
    valid_country_codes.insert(681); // Wallis and Futuna
    valid_country_codes.insert(682); // Cook Islands
    valid_country_codes.insert(683); // Niue
    valid_country_codes.insert(684); // American Samoa
    valid_country_codes.insert(685); // Samoa
    valid_country_codes.insert(686); // Kiribati
    valid_country_codes.insert(687); // New Caledonia
    valid_country_codes.insert(688); // Tuvalu
    valid_country_codes.insert(689); // French Polynesia
    valid_country_codes.insert(690); // Tokelau
    valid_country_codes.insert(691); // Micronesia
    valid_country_codes.insert(692); // Marshall Islands
    valid_country_codes.insert(850); // North Korea
    valid_country_codes.insert(852); // Hong Kong
    valid_country_codes.insert(853); // Macau
    valid_country_codes.insert(855); // Cambodia
    valid_country_codes.insert(856); // Laos
    valid_country_codes.insert(880); // Bangladesh
    valid_country_codes.insert(886); // Taiwan
    valid_country_codes.insert(960); // Maldives
    valid_country_codes.insert(961); // Lebanon
    valid_country_codes.insert(962); // Jordan
    valid_country_codes.insert(963); // Syria
    valid_country_codes.insert(964); // Iraq
    valid_country_codes.insert(965); // Kuwait
    valid_country_codes.insert(966); // Saudi Arabia
    valid_country_codes.insert(967); // Yemen
    valid_country_codes.insert(968); // Oman
    valid_country_codes.insert(970); // Palestine
    valid_country_codes.insert(971); // UAE
    valid_country_codes.insert(972); // Israel
    valid_country_codes.insert(973); // Bahrain
    valid_country_codes.insert(974); // Qatar
    valid_country_codes.insert(975); // Bhutan
    valid_country_codes.insert(976); // Mongolia
    valid_country_codes.insert(977); // Nepal
    valid_country_codes.insert(992); // Tajikistan
    valid_country_codes.insert(993); // Turkmenistan
    valid_country_codes.insert(994); // Azerbaijan
    valid_country_codes.insert(995); // Georgia
    valid_country_codes.insert(996); // Kyrgyzstan
    valid_country_codes.insert(998); // Uzbekistan

    Self { valid_country_codes }
  }

  /// Extract country code from phone number
  fn extract_country_code(&self, phone_number: u64) -> Option<u16> {
    let phone_str = phone_number.to_string();
    
    // Try 3-digit country codes first
    if phone_str.len() >= 3 {
      if let Ok(code) = phone_str[..3].parse::<u16>() {
        if self.valid_country_codes.contains(&code) {
          return Some(code);
        }
      }
    }
    
    // Try 2-digit country codes
    if phone_str.len() >= 2 {
      if let Ok(code) = phone_str[..2].parse::<u16>() {
        if self.valid_country_codes.contains(&code) {
          return Some(code);
        }
      }
    }
    
    // Try 1-digit country codes
    if phone_str.len() >= 1 {
      if let Ok(code) = phone_str[..1].parse::<u16>() {
        if self.valid_country_codes.contains(&code) {
          return Some(code);
        }
      }
    }
    
    None
  }

  /// Validate phone number format and range
  pub fn validate_phone_number(&self, phone_number: u64) -> Result<(), FriendshipError> {
    // Check basic range
    if phone_number < ValidationConstants::MIN_PHONE_NUMBER {
      return Err(FriendshipError::InvalidInput(format!(
        "Phone number too short: {} (minimum {} digits)",
        phone_number,
        ValidationConstants::MIN_PHONE_NUMBER.to_string().len()
      )));
    }

    if phone_number > ValidationConstants::MAX_PHONE_NUMBER {
      return Err(FriendshipError::InvalidInput(format!(
        "Phone number too long: {} (maximum {} digits)",
        phone_number,
        ValidationConstants::MAX_PHONE_NUMBER.to_string().len()
      )));
    }

    // Validate country code
    if self.extract_country_code(phone_number).is_none() {
      return Err(FriendshipError::InvalidInput(
        "Invalid country code in phone number".to_string(),
      ));
    }

    // Check for suspicious patterns (all same digits, sequential digits)
    let phone_str = phone_number.to_string();
    
    // Check for all same digits (e.g., 1111111111)
    if phone_str.chars().all(|c| c == phone_str.chars().next().unwrap()) {
      return Err(FriendshipError::InvalidInput(
        "Phone number cannot be all same digits".to_string(),
      ));
    }

    // Check for simple sequential patterns (e.g., 1234567890)
    let digits: Vec<u32> = phone_str.chars().filter_map(|c| c.to_digit(10)).collect();
    if digits.len() > 5 {
      let mut is_sequential = true;
      for i in 1..digits.len() {
        if digits[i] != (digits[i-1] + 1) % 10 {
          is_sequential = false;
          break;
        }
      }
      if is_sequential {
        return Err(FriendshipError::InvalidInput(
          "Phone number cannot be sequential digits".to_string(),
        ));
      }
    }

    Ok(())
  }

  /// Validate a collection of phone numbers
  pub fn validate_phone_numbers(&self, phone_numbers: &[u64]) -> Result<(), FriendshipError> {
    for phone_number in phone_numbers {
      self.validate_phone_number(*phone_number)?;
    }
    Ok(())
  }
}

impl Default for PhoneNumberValidator {
  fn default() -> Self {
    Self::new()
  }
}

impl Validator<u64> for PhoneNumberValidator {
  fn validate(&self, phone_number: &u64) -> Result<(), FriendshipError> {
    self.validate_phone_number(*phone_number)
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_valid_phone_numbers() {
    let validator = PhoneNumberValidator::new();
    
    // Valid phone numbers from different countries
    assert!(validator.validate_phone_number(12345678901).is_ok()); // US
    assert!(validator.validate_phone_number(971501234567).is_ok()); // UAE
    assert!(validator.validate_phone_number(447700900123).is_ok()); // UK
    assert!(validator.validate_phone_number(8612345678901).is_ok()); // China
  }

  #[test]
  fn test_invalid_phone_numbers() {
    let validator = PhoneNumberValidator::new();
    
    // Too short
    assert!(validator.validate_phone_number(123456).is_err());
    
    // Too long
    assert!(validator.validate_phone_number(12345678901234567890).is_err());
    
    // All same digits
    assert!(validator.validate_phone_number(11111111111).is_err());
    
    // Sequential digits
    assert!(validator.validate_phone_number(12345678901).is_err());
    
    // Invalid country code
    assert!(validator.validate_phone_number(99912345678).is_err());
  }

  #[test]
  fn test_extract_country_code() {
    let validator = PhoneNumberValidator::new();
    
    assert_eq!(validator.extract_country_code(12345678901), Some(1)); // US
    assert_eq!(validator.extract_country_code(971501234567), Some(971)); // UAE
    assert_eq!(validator.extract_country_code(447700900123), Some(44)); // UK
    assert_eq!(validator.extract_country_code(99912345678), None); // Invalid
  }

  #[test]
  fn test_validate_phone_numbers_collection() {
    let validator = PhoneNumberValidator::new();
    let phone_numbers = vec![12345678901, 971501234567, 447700900123];
    
    assert!(validator.validate_phone_numbers(&phone_numbers).is_ok());
    
    let invalid_phone_numbers = vec![12345678901, 123456]; // One invalid
    assert!(validator.validate_phone_numbers(&invalid_phone_numbers).is_err());
  }
}
