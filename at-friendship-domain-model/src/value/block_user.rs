use at_internal_deps::at_friendship::*;

use at_lib_macros::at_cvm;

at_cvm! {
  /// Block User Status
  #[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
  BlockUserStatus<u8, str> {
    /// Unblocked
    Unblocked(0, "Unblocked", "Unblocked"),
    /// Blocked
    Blocked(1, "Blocked", "Blocked"),
  }
}

impl From<u8> for BlockUserStatus {
  fn from(value: u8) -> Self {
    match value {
      0 => BlockUserStatus::Unblocked,
      1 => BlockUserStatus::Blocked,
      _ => BlockUserStatus::Unblocked,
    }
  }
}
