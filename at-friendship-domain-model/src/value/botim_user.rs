use at_internal_deps::at_friendship::*;

use at_lib_macros::at_wrapper;

use crate::all::*;

use std::{fmt,
          fmt::Display};

at_wrapper! {
  /// Botim User ID
  #[derive(Debug, <PERSON>lone, PartialEq, Eq)]
  BotimUserId(Auid);
}

impl Display for BotimUserId {
  fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
    write!(f, "{}", self.0)
  }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct BotimUser {
  pub botim_user_id: BotimUserId,
  pub auid_repr:     AuidRepr,
  pub phone_number:  PhoneNumber,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RegisterEvent {
  /// Register Botim User
  Register,
  /// Unregister Botim User
  Unregister,
}
