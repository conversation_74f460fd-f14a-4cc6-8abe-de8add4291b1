//! Value objects module
use at_internal_deps::at_friendship::*;

use {at_lib_comm_domain::id::uuid::UuidBytes,
     at_lib_macros::{at_cvm,
                     at_wrapper}};

use std::{fmt,
          fmt::Display};

at_wrapper! {
  /// Contact Entity Id
  #[derive(Debug, Clone, PartialEq, Eq)]
  ContactId(UuidBytes);

  /// AstraTech Unique User ID, common sense in all user systems
  #[derive(Debug, Clone, PartialEq, Eq)]
  Auid(UuidBytes);

  /// Contact Owner
  #[derive(Debug, Clone, PartialEq, Eq)]
  ContactOwnerId(Auid);

  /// Phone Number
  #[derive(Debug, Clone, PartialEq, Eq, Hash)]
  PhoneNumber(u64);

  /// Contact Name
  #[derive(Debug, Clone, PartialEq, Eq)]
  ContactName(String);

  /// Auid Repr
  #[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
  AuidRepr(String);
}

at_cvm! {
  /// Contact Status
  #[derive(Debug, <PERSON><PERSON>, PartialEq, Eq)]
  ContactStatus<u8, str> {
    /// Deleted
    Deleted(0, "Deleted", "Deleted"),
    /// Normal
    Normal(1, "Normal", "Normal"),
  }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct ContactInfo {
  pub phone_number: PhoneNumber,
  pub name:         Option<ContactName>,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct ContactOperation {
  pub owner_id: ContactOwnerId,
  pub add:      Vec<ContactInfo>,
  pub delete:   Vec<ContactInfo>,
  pub update:   Vec<ContactInfo>,
}

impl Display for Auid {
  fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
    write!(f, "{}", self.0)
  }
}

impl Display for AuidRepr {
  fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
    write!(f, "{}", self.0)
  }
}

impl From<u8> for ContactStatus {
  fn from(value: u8) -> Self {
    match value {
      0 => ContactStatus::Deleted,
      1 => ContactStatus::Normal,
      _ => ContactStatus::Normal,
    }
  }
}

impl Display for ContactOwnerId {
  fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
    write!(f, "{}", self.0)
  }
}

impl Display for PhoneNumber {
  fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
    write!(f, "{}", self.0)
  }
}
