use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     std::sync::Arc};

use async_trait::async_trait;

pub struct BlockedUserApplicationImpl {
  pub blocked_user_repo: Arc<dyn BlockedUserRepo>,
}

#[async_trait]
impl DefaultBlockedUserApplication for BlockedUserApplicationImpl {
  fn deps(&self) -> &dyn DefaultBlockedUserApplicationDependencies {
    self
  }
}

#[async_trait]
impl DefaultBlockedUserApplicationDependencies for BlockedUserApplicationImpl {
  fn blocked_user_repo(&self) -> &dyn BlockedUserRepo {
    self.blocked_user_repo.as_ref()
  }
}
