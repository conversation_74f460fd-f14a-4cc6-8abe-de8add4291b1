use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     std::sync::Arc};

use async_trait::async_trait;

#[derive(Clone)]
pub struct BotimUserApplicationImpl {
  pub botim_user_repo:   Arc<dyn BotimUserRepo>,
  pub auid_repo:         Arc<dyn AuidRepo>,
  pub contact_repo:      Arc<dyn ContactRepo>,
  pub notification_repo: Arc<dyn NotificationRepo>,
  pub task_mgmt_repo:    Arc<dyn TaskMgmtRepo>,
}

#[async_trait]
impl DefaultBotimUserApplication for BotimUserApplicationImpl {
  fn deps(&self) -> Arc<dyn DefaultBotimUserApplicationDependencies> {
    Arc::new(self.clone())
  }
}

#[async_trait]
impl DefaultBotimUserApplicationDependencies for BotimUserApplicationImpl {
  fn botim_user_repo(&self) -> &dyn BotimUserRepo {
    self.botim_user_repo.as_ref()
  }

  fn auid_repo(&self) -> &dyn AuidRepo {
    self.auid_repo.as_ref()
  }

  fn contact_repo(&self) -> &dyn ContactRepo {
    self.contact_repo.as_ref()
  }

  fn notification_repo(&self) -> &dyn NotificationRepo {
    self.notification_repo.as_ref()
  }

  fn task_mgmt_repo(&self) -> &dyn TaskMgmtRepo {
    self.task_mgmt_repo.as_ref()
  }
}
