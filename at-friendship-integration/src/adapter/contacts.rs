use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     std::sync::Arc};

use async_trait::async_trait;

pub struct ContactsApplicationImpl {
  pub contact_repo:    Arc<dyn ContactRepo>,
  pub botim_user_repo: Arc<dyn BotimUserRepo>,
}

#[async_trait]
impl ContactsApplicationDependencies for ContactsApplicationImpl {
  fn contact_repo(&self) -> &dyn ContactRepo {
    self.contact_repo.as_ref()
  }

  fn botim_user_repo(&self) -> &dyn BotimUserRepo {
    self.botim_user_repo.as_ref()
  }
}

#[async_trait]
impl DefaultContactsApplication for ContactsApplicationImpl {
  fn deps(&self) -> &dyn ContactsApplicationDependencies {
    self
  }
}
