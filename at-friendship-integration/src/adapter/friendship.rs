use {crate::prelude::*,
     at_friendship_domain_model::all::*,
     at_friendship_repo::all::*,
     at_janus_client::prelude::*,
     sqlx::{MySql,
            Pool},
     std::sync::Arc};

#[derive(Clone)]
pub struct FriendshipAdapter {
  pub contacts_app:     Arc<dyn ContactsApplication>,
  pub botim_user_app:   Arc<dyn BotimUserApplication>,
  pub blocked_user_app: Arc<dyn BlockedUserApplication>,
}

impl FriendshipAdapter {
  pub fn new(
    mysql_pool: Pool<MySql>,
    tidb_pools: Vec<Pool<MySql>>,
    janus_client: JanusClient<EtcdServiceFetcher>,
    redis_pool: fred::clients::Pool,
  ) -> Self {
    // Always use sharded TiDB pools for contacts
    let contact_repo: Arc<dyn ContactRepo> = Arc::new(ContactShardedRepoImpl::new(tidb_pools));

    let auid_repo: Arc<dyn AuidRepo> = Arc::new(AuidRepoImpl::new(janus_client.clone()));

    // Use MySQL pool for other repositories
    let botim_user_repo: Arc<dyn BotimUserRepo> =
      Arc::new(BotimUserRepoImpl::new(mysql_pool.clone(), redis_pool.clone()));
    let blocked_user_repo: Arc<dyn BlockedUserRepo> =
      Arc::new(BlockedUserRepoImpl::new(mysql_pool, redis_pool.clone()));
    let notification_repo: Arc<dyn NotificationRepo> = Arc::new(NotificationRepoImpl::new(janus_client.clone()));
    let task_mgmt_repo: Arc<dyn TaskMgmtRepo> = Arc::new(TaskMgmtRepoImpl::new(redis_pool));

    // create all apps
    let botim_user_app: Arc<dyn BotimUserApplication> = Arc::new(BotimUserApplicationImpl {
      botim_user_repo: botim_user_repo.clone(),
      auid_repo,
      contact_repo: contact_repo.clone(),
      notification_repo,
      task_mgmt_repo,
    });
    let contacts_app: Arc<dyn ContactsApplication> = Arc::new(ContactsApplicationImpl {
      contact_repo: contact_repo.clone(),
      botim_user_repo,
    });

    let blocked_user_app: Arc<dyn BlockedUserApplication> = Arc::new(BlockedUserApplicationImpl {
      blocked_user_repo: blocked_user_repo.clone(),
    });

    Self {
      contacts_app,
      botim_user_app,
      blocked_user_app,
    }
  }

  pub async fn get_owned_botim_users(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
    task_id: &Option<String>,
  ) -> FriendshipResult<(Vec<BotimUserEntity>, bool)> {
    self
      .botim_user_app
      .get_owned_botim_users(owner_id, phone_number_filters, task_id)
      .await
  }

  pub async fn sync_contacts(&self, contact_operation: ContactOperation) -> FriendshipResult<String> {
    let phone_numbers = self.contacts_app.sync_contacts(contact_operation).await?;
    let task_id = self
      .botim_user_app
      .save_botim_users_in_background(&phone_numbers)
      .await?;
    Ok(task_id)
  }

  pub async fn get_existing_contact_phone_numbers(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<PhoneNumber>> {
    self
      .contacts_app
      .get_existing_contact_phone_numbers(owner_id, phone_number_filters)
      .await
  }

  pub async fn get_existing_contact_owners(
    &self,
    botim_user_id: &BotimUserId,
    owner_ids: &[ContactOwnerId],
  ) -> FriendshipResult<Vec<ContactOwnerId>> {
    self
      .contacts_app
      .get_existing_contact_owners(botim_user_id, owner_ids)
      .await
  }

  pub async fn save_blocked_user(
    &self,
    user_id: &BotimUserId,
    blocked_user_id: &BotimUserId,
    status: BlockUserStatus,
  ) -> FriendshipResult<()> {
    self
      .blocked_user_app
      .save_blocked_user(user_id, blocked_user_id, status)
      .await
  }

  pub async fn get_blocked_users(&self, user_id: &BotimUserId) -> FriendshipResult<Vec<BlockedUserEntity>> {
    self.blocked_user_app.get_blocked_users(user_id).await
  }

  pub async fn save_botim_user(&self, botim_users: &[BotimUserEntity]) -> FriendshipResult<()> {
    self.botim_user_app.save_botim_user(botim_users).await
  }

  pub async fn is_blocked(&self, user_id: &BotimUserId, blocked_user_id: &BotimUserId) -> FriendshipResult<bool> {
    self.blocked_user_app.is_blocked(user_id, blocked_user_id).await
  }

  pub async fn is_blocked_by_users(
    &self,
    user_ids: &[BotimUserId],
    blocked_user_id: &BotimUserId,
  ) -> FriendshipResult<Vec<BotimUserId>> {
    self
      .blocked_user_app
      .is_blocked_by_users(user_ids, blocked_user_id)
      .await
  }

  pub async fn new_botim_user_notification(
    &self,
    botim_user_entity: &BotimUserEntity,
    register_event: RegisterEvent,
  ) -> FriendshipResult<()> {
    self
      .botim_user_app
      .new_botim_user_notification(botim_user_entity, register_event)
      .await
  }
}
