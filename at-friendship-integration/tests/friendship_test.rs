use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_friendship_protocol_pb::prelude::*,
     at_friendship_stub::prelude::*,
     at_internal_deps::at_friendship::*,
     at_janus_client::prelude::*,
     at_lib_comm_domain::id::uuid::UuidBytes,
     at_otel_integration::prelude::*,
     tracing::info};

#[cfg(test)]
mod tests {
  // Helper function to create test environment
  use super::*;

  fn init_test_logger() {
    let log_config = LogConfigDTO {
      name:           "at-group-repo".to_string(),
      output:         "console://stdout".to_string(),
      log_level:      Some("debug".to_string()),
      log_level_list: None,
      line_jsonfy:    None,
    };
    let tracing_config = TracingConfigDTO {
      endpoint:                    "http://localhost:4317".to_string(),
      service_name:                Some("at-friendship-test".to_string()),
      service_version:             Some("1.0.0".to_string()),
      organization:                None,
      stream_name:                 None,
      deployment_environment:      None,
      headers:                     std::collections::HashMap::new(),
      periodic_read_interval_secs: 60,
      trace_filter:                None,
    };
    let log_initializer = OtelLogAdapter::new();
    let _ = log_initializer.init_tracing(&[log_config], &tracing_config);
  }

  async fn setup_test_env() -> StubResult<JanusClient<EtcdServiceFetcher>> {
    unsafe {
      std::env::set_var("APP_ID", "at-friendship-server-grpc-local");
      std::env::set_var("APOLLO_CONFIG_SERVICE", "http://*********:8080");
      std::env::set_var("OTEL_METRIC_EXPORT_INTERVAL", "0");
      std::env::set_var("PROMETHEUS_LISTEN_PORT", "0");
      std::env::set_var("OTEL_METRICS_EXPORTER", "none");
    }

    // init log
    // OtelLogAdapter::init_with_apollo_config().await?;
    init_test_logger();

    // Create client
    let janus_client = JanusClientFactory::new_with_default_config_loader()
      .await
      .map_err(|e| StubError::Dispatch("at-friendship".to_string(), e.to_string()))?;
    let janus_client = janus_client
      .create_janus_client()
      .await
      .map_err(|e| StubError::Dispatch("at-friendship".to_string(), e.to_string()))?;
    Ok(janus_client)
  }

  #[tokio::test]
  #[ignore]
  async fn integration_test() -> StubResult<()> {
    let janus_client = setup_test_env().await?;
    test_sync_contacts_add_only(janus_client.clone()).await?;
    test_sync_contacts_full(janus_client.clone()).await?;
    test_get_botim_users(janus_client.clone()).await?;

    Ok(())
  }

  async fn test_sync_contacts_add_only(janus_client: JanusClient<EtcdServiceFetcher>) -> StubResult<()> {
    let user_id = Auid::new(UuidBytes::generate()).inner_ref().inner_ref().to_vec();

    // Create sync request with only add operation
    let payload = SyncContactsRequest {
      owner_auid: user_id.clone(),
      operation:  Some(SyncContactsOperation {
        add:    vec![
          Contact {
            name:         Some("John".to_string()),
            phone_number: 13800138000u64,
          },
          Contact {
            name:         Some("Alice".to_string()),
            phone_number: 13900139000u64,
          },
        ],
        delete: vec![],
        update: vec![],
      }),
      task_id:    None,
    };

    // Encode request
    let friendship_stub = FriendshipStub::new(janus_client);
    let response = friendship_stub.sync_contacts("test", "test", &payload).await?;
    info!("Sync contacts response: {:?}", response);

    // Add assertions to verify the response
    assert!(response.success, "Response should be successful");

    Ok(())
  }

  async fn test_sync_contacts_full(janus_client: JanusClient<EtcdServiceFetcher>) -> StubResult<()> {
    let user_id = Auid::new(UuidBytes::generate()).inner_ref().inner_ref().to_vec();

    // Create sync request with all operation types
    let payload = SyncContactsRequest {
      owner_auid: user_id.clone(),
      operation:  Some(SyncContactsOperation {
        add:    vec![
          Contact {
            name:         Some("John".to_string()),
            phone_number: 971500000000u64,
          },
          Contact {
            name:         Some("Alice".to_string()),
            phone_number: 971500000001u64,
          },
        ],
        delete: vec![Contact {
          name:         Some("Bob".to_string()),
          phone_number: 971500000002u64,
        }],
        update: vec![Contact {
          name:         Some("Tom".to_string()),
          phone_number: 971500000003u64,
        }],
      }),
      task_id:    None,
    };

    // Encode request

    let friendship_stub = FriendshipStub::new(janus_client);
    let response = friendship_stub.sync_contacts("test", "test", &payload).await?;
    info!("Sync contacts response: {:?}", response);

    // Add assertions to verify the response
    assert!(response.success, "Response should be successful");

    Ok(())
  }

  async fn test_get_botim_users(janus_client: JanusClient<EtcdServiceFetcher>) -> StubResult<()> {
    let user_id = Auid::new(UuidBytes::generate()).inner_ref().inner_ref().to_vec();

    let payload = GetOwnedBotimUsersRequest {
      owner_auid:           user_id.clone(),
      phone_number_filters: vec![
        971522634597u64,
        971585789757u64,
        971585991024u64,
        971501635523u64,
        8613818512229u64,
        8613811705569u64,
        8618668011224u64,
        8617840293558u64,
        8617840287263u64,
        86182499895u64,
        971585965208u64,
        971501626491u64,
        971526049665u64,
        8613818512224u64,
        8618668011275u64,
        8613387512935u64,
        8618668011264u64,
        971585780083u64,
        8618600501703u64,
        971585789001u64,
        971521231235u64,
        971500000001u64,
        971500000000u64,
        971585789002u64,
        971521236789u64,
        971585789001u64,
        971585789002u64,
      ],
      task_id:              None,
    };

    let friendship_stub = FriendshipStub::new(janus_client);
    let response = friendship_stub.get_owned_botim_users("test", "test", &payload).await?;
    info!("Get owned botim users result: {:?}", response);

    Ok(())
  }
}
