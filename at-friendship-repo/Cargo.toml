[package]
name = "at-friendship-repo"
version.workspace = true
edition.workspace = true
authors.workspace = true
repository.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true

[dependencies]
# dep-mgmt
at-deps = { workspace = true }
at-internal-deps = { workspace = true }

## internal dependencies
at-friendship-domain-model = { workspace = true }
at-friendship-protocol-pb = { workspace = true }
at-msg-receiver-stub = { workspace = true }
at-msg-protocol-pb = { workspace = true }
at-janus-client = { workspace = true }
at-auid-protocol-pb = { workspace = true }
at-auid-stub = { workspace = true }

## exceptional
serde = { workspace = true }
serde_json = { workspace = true }
sqlx = { workspace = true }
fred = { workspace = true }
futures = { workspace = true }
hex = { workspace = true }
tracing = { workspace = true }
