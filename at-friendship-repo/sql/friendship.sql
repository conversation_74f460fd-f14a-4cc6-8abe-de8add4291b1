USE friendship;

CREATE TABLE friendship.t_contacts (
    id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'Auto-incrementing primary key',
    contact_id BINARY(16) NOT NULL COMMENT 'Contact ID',
    owner_id BINARY(16) NOT NULL COMMENT 'User owner ID',
    phone_number BIGINT UNSIGNED NOT NULL COMMENT 'User phone number',
    contact_name VARCHAR(100) NULL COMMENT 'Contact name',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Contact status (0=Deleted, 1=Normal)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    PRIMARY KEY (id),
    UNIQUE KEY uniq_t_contact_id(contact_id),
    UNIQUE KEY uniq_t_owner_phone (owner_id, phone_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Contact table';

CREATE TABLE friendship.t_botim_users (
    id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'Auto-incrementing primary key',
    botim_user_id BINARY(16) NOT NULL COMMENT 'Botim user ID',
    phone_number BIGINT UNSIGNED NOT NULL COMMENT 'Phone number',
    auid_repr VARCHAR(100) NOT NULL COMMENT 'Auid repr',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    UNIQUE KEY uniq_t_botim_user_id(botim_user_id),
    primary key (id),
    INDEX idx_t_phone_number (phone_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Botim user table';

CREATE TABLE friendship.t_blocked_users (
    id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'Auto-incrementing primary key',
    user_id BINARY(16) NOT NULL COMMENT 'User ID',
    blocked_user_id BINARY(16) NOT NULL COMMENT 'Blocked user ID',
    status TINYINT UNSIGNED NOT NULL COMMENT 'Blocked user status',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    primary key (id),
    UNIQUE KEY uniq_t_user_id_blocked_user_id(user_id, blocked_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Blocked user table';
