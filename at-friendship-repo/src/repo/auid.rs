use {async_trait::async_trait,
     at_auid_protocol_pb::prelude::*,
     at_auid_stub::prelude::stub::*,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_internal_deps::at_friendship::*,
     at_janus_client::prelude::*,
     at_lib_comm_domain::id::uuid::UuidBytes,
     tracing::{error,
               info}};

pub const ADN_AT_AUID_SERVICE_NAME: &str = "3";
pub const ADM_AUID_CHECK_MOBILE_STATUS: &str = "0";
pub struct AuidRepoImpl {
  mobile_status_stub: MobileStatusStub,
}

impl AuidRepoImpl {
  pub fn new(janus_client: JanusClient<EtcdServiceFetcher>) -> Self {
    Self {
      mobile_status_stub: MobileStatusStub::new(janus_client),
    }
  }
}

#[async_trait]
impl AuidRepo for AuidRepoImpl {
  #[tracing::instrument(name = "get_auids_by_phone_numbers", skip(self, phone_numbers))]
  async fn get_auids_by_phone_numbers(
    &self,
    phone_numbers: &[PhoneNumber],
  ) -> FriendshipResult<Vec<(PhoneNumber, Auid, AuidRepr)>> {
    let pb_request = CheckMobileStatusCommand {
      mobile_numbers: phone_numbers
        .iter()
        .map(|phone_number| phone_number.inner_ref().to_string())
        .collect(),
    };

    let response = self
      .mobile_status_stub
      .check_status_by_mobile_number(ADN_AT_AUID_SERVICE_NAME, ADM_AUID_CHECK_MOBILE_STATUS, &pb_request)
      .await
      .map_err(|e| {
        error!("Failed to get auids by phone numbers: {:?}", e);
        FriendshipError::RepoError(e.to_string())
      })?;

    let result: Vec<(PhoneNumber, Auid, AuidRepr)> = response
      .mobile_statuses
      .iter()
      .filter(|status| status.registered)
      .filter_map(|status| {
        let phone_number = match status.mobile_number.parse::<u64>() {
          Ok(num) => PhoneNumber::new(num),
          Err(_) => {
            error!("Invalid phone number format: {}", status.mobile_number);
            return None;
          }
        };
        let auid_bytes: [u8; 16] = match status.auid.as_slice().try_into() {
          Ok(bytes) => bytes,
          Err(_) => {
            error!("Invalid auid format for phone number {}", status.mobile_number);
            return None;
          }
        };
        let auid = Auid::new(UuidBytes::new(auid_bytes));
        let auid_repr = AuidRepr::new(status.repr.clone());
        Some((phone_number, auid, auid_repr))
      })
      .collect();

    info!(
      "check mobile status for {} phone numbers, and return {} auids",
      phone_numbers.len(),
      result.len()
    );
    Ok(result)
  }
}
