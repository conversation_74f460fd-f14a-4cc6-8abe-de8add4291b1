//! Base repository with common functionality
//!
//! This module provides a base repository trait and implementation
//! with common database operations and error handling patterns.

use {crate::repo::{batch_operations::{BatchConfig,
                                      BatchOperations},
                   query_builder::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                                   SafeInClauseBuilder}},
     at_friendship_domain_model::{all::FriendshipError,
                                  errors::secure_error::SecureErrorHandler},
     sqlx::{MySql,
            Pool},
     std::{collections::HashMap,
           time::Duration},
     tracing::{debug,
               error,
               info}};

/// Base repository trait with common operations
#[async_trait::async_trait]
pub trait BaseRepository: Send + Sync {
  /// Get the database pool
  fn get_pool(&self) -> &Pool<MySql>;

  /// Get repository name for logging
  fn get_name(&self) -> &'static str;

  /// Execute a query with error handling and logging
  async fn execute_query<F, T>(&self, operation_name: &str, query_fn: F) -> Result<T, FriendshipError>
  where
    F: FnOnce(&Pool<MySql>) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, sqlx::Error>> + Send>>
      + Send,
    T: Send,
  {
    let start = std::time::Instant::now();
    debug!("Executing {} operation in {}", operation_name, self.get_name());

    let result = query_fn(self.get_pool()).await;

    let duration = start.elapsed();
    match &result {
      Ok(_) => {
        info!(
          "{} operation in {} completed successfully in {:?}",
          operation_name,
          self.get_name(),
          duration
        );
      }
      Err(e) => {
        error!(
          "{} operation in {} failed after {:?}: {:?}",
          operation_name,
          self.get_name(),
          duration,
          e
        );
      }
    }

    result.map_err(|e| SecureErrorHandler::sanitize_database_error(&e, operation_name))
  }



  /// Execute batch operation with chunking
  async fn execute_batch_operation<T, F>(
    &self,
    items: Vec<T>,
    operation_name: &str,
    batch_fn: F,
    batch_size: Option<usize>,
  ) -> Result<Vec<sqlx::mysql::MySqlRow>, FriendshipError>
  where
    T: Clone + Send + Sync,
    F: Fn(&[T]) -> Result<String, FriendshipError> + Send + Sync,
  {
    let batch_size = batch_size.unwrap_or(1000);
    info!(
      "Executing batch {} operation in {} with {} items, batch size {}",
      operation_name,
      self.get_name(),
      items.len(),
      batch_size
    );

    BatchOperations::batch_select(self.get_pool(), items, batch_size, batch_fn).await
  }

  /// Validate input parameters
  fn validate_input<T>(&self, collection: &[T], field_name: &str, max_size: usize) -> Result<(), FriendshipError> {
    QueryValidator::validate_collection_size(collection, max_size, field_name)
  }

  /// Build safe IN clause query
  fn build_safe_in_query(&self, base_query: &str, param_count: usize) -> Result<String, FriendshipError> {
    SafeInClauseBuilder::build_in_query(base_query, param_count)
  }
}

/// Default implementation for common repository operations
pub struct BaseRepositoryImpl {
  pool: Pool<MySql>,
  name: &'static str,
}

impl BaseRepositoryImpl {
  pub fn new(pool: Pool<MySql>, name: &'static str) -> Self {
    Self { pool, name }
  }
}

#[async_trait::async_trait]
impl BaseRepository for BaseRepositoryImpl {
  fn get_pool(&self) -> &Pool<MySql> {
    &self.pool
  }

  fn get_name(&self) -> &'static str {
    self.name
  }
}

/// Repository factory for creating repositories with common configuration
pub struct RepositoryFactory;

impl RepositoryFactory {
  /// Create a repository with standard configuration
  pub fn create_repository(pool: Pool<MySql>, name: &'static str) -> BaseRepositoryImpl {
    BaseRepositoryImpl::new(pool, name)
  }

  /// Create multiple repositories for sharded databases
  pub fn create_sharded_repositories(
    pools: Vec<Pool<MySql>>,
    base_name: &'static str,
  ) -> Vec<BaseRepositoryImpl> {
    pools
      .into_iter()
      .enumerate()
      .map(|(i, pool)| {
        let name = Box::leak(format!("{}_shard_{}", base_name, i).into_boxed_str());
        BaseRepositoryImpl::new(pool, name)
      })
      .collect()
  }
}

/// Common error handling patterns
pub struct ErrorHandler;

impl ErrorHandler {
  /// Handle database connection errors
  pub fn handle_connection_error(error: &sqlx::Error, operation: &str) -> FriendshipError {
    match error {
      sqlx::Error::PoolTimedOut => {
        error!("Database pool timeout during {}", operation);
        FriendshipError::DatabaseError("Service temporarily unavailable".to_string())
      }
      sqlx::Error::PoolClosed => {
        error!("Database pool closed during {}", operation);
        FriendshipError::DatabaseError("Service unavailable".to_string())
      }
      _ => {
        error!("Database connection error during {}: {:?}", operation, error);
        FriendshipError::DatabaseError("Database connection failed".to_string())
      }
    }
  }

  /// Handle query execution errors
  pub fn handle_query_error(error: &sqlx::Error, operation: &str) -> FriendshipError {
    match error {
      sqlx::Error::RowNotFound => {
        debug!("No rows found during {}", operation);
        FriendshipError::DatabaseError("Record not found".to_string())
      }
      sqlx::Error::Database(db_err) => {
        error!("Database constraint error during {}: {}", operation, db_err);
        if db_err.message().contains("Duplicate entry") {
          FriendshipError::DatabaseError("Duplicate record".to_string())
        } else if db_err.message().contains("foreign key constraint") {
          FriendshipError::DatabaseError("Invalid reference".to_string())
        } else {
          FriendshipError::DatabaseError("Database constraint violation".to_string())
        }
      }
      _ => {
        error!("Query execution error during {}: {:?}", operation, error);
        FriendshipError::DatabaseError("Query execution failed".to_string())
      }
    }
  }
}

/// Transaction utilities
pub struct TransactionUtils;

impl TransactionUtils {
  /// Execute operation within a transaction
  pub async fn execute_in_transaction<F, T>(
    pool: &Pool<MySql>,
    operation_name: &str,
    operation: F,
  ) -> Result<T, FriendshipError>
  where
    F: for<'a> FnOnce(
      &'a mut sqlx::Transaction<'_, MySql>,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, sqlx::Error>> + Send + 'a>>
      + Send,
    T: Send,
  {
    let start = std::time::Instant::now();
    info!("Starting transaction for {}", operation_name);

    let mut tx = pool.begin().await.map_err(|e| {
      error!("Failed to begin transaction for {}: {:?}", operation_name, e);
      ErrorHandler::handle_connection_error(&e, operation_name)
    })?;

    let result = operation(&mut tx).await;

    match result {
      Ok(value) => {
        tx.commit().await.map_err(|e| {
          error!("Failed to commit transaction for {}: {:?}", operation_name, e);
          ErrorHandler::handle_query_error(&e, operation_name)
        })?;

        let duration = start.elapsed();
        info!("Transaction for {} completed successfully in {:?}", operation_name, duration);
        Ok(value)
      }
      Err(e) => {
        if let Err(rollback_err) = tx.rollback().await {
          error!("Failed to rollback transaction for {}: {:?}", operation_name, rollback_err);
        } else {
          info!("Transaction for {} rolled back successfully", operation_name);
        }

        let duration = start.elapsed();
        error!("Transaction for {} failed after {:?}: {:?}", operation_name, duration, e);
        Err(ErrorHandler::handle_query_error(&e, operation_name))
      }
    }
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_repository_factory() {
    // This test would require a real database connection, so we'll just test the structure
    assert_eq!(std::mem::size_of::<BaseRepositoryImpl>(), std::mem::size_of::<Pool<MySql>>() + std::mem::size_of::<&'static str>());
  }

  #[test]
  fn test_error_handling() {
    let error = sqlx::Error::RowNotFound;
    let handled = ErrorHandler::handle_query_error(&error, "test_operation");
    
    match handled {
      FriendshipError::DatabaseError(msg) => assert_eq!(msg, "Record not found"),
      _ => panic!("Expected DatabaseError"),
    }
  }
}
