//! Batch operations for improved performance
//!
//! This module provides optimized batch operations for database queries
//! to reduce round-trips and improve overall performance.

use {crate::repo::query_builder::{QueryVali<PERSON><PERSON>,
                                  SafeInClauseBuilder},
     at_friendship_domain_model::all::*,
     sqlx::{MySql,
            Pool,
            QueryBuilder},
     std::collections::HashMap,
     tracing::{debug,
               info}};

/// Batch operation configuration
#[derive(Debug, Clone)]
pub struct BatchConfig {
  /// Maximum batch size for single operation
  pub max_batch_size: usize,
  /// Whether to use transactions for batch operations
  pub use_transactions: bool,
  /// Timeout for batch operations in seconds
  pub timeout_seconds: u64,
}

impl BatchConfig {
  /// Default configuration for batch operations
  pub fn default() -> Self {
    Self {
      max_batch_size: 1000,
      use_transactions: true,
      timeout_seconds: 30,
    }
  }

  /// Configuration optimized for high throughput
  pub fn high_throughput() -> Self {
    Self {
      max_batch_size: 5000,
      use_transactions: false, // Skip transactions for better performance
      timeout_seconds: 60,
    }
  }

  /// Configuration optimized for consistency
  pub fn high_consistency() -> Self {
    Self {
      max_batch_size: 500,
      use_transactions: true,
      timeout_seconds: 15,
    }
  }
}

/// Batch operation utilities
pub struct BatchOperations;

impl BatchOperations {
  /// Execute batch SELECT operations with chunking
  pub async fn batch_select<T, F>(
    pool: &Pool<MySql>,
    items: Vec<T>,
    batch_size: usize,
    query_fn: F,
  ) -> FriendshipResult<Vec<sqlx::mysql::MySqlRow>>
  where
    T: Clone + Send + Sync,
    F: Fn(&[T]) -> FriendshipResult<String> + Send + Sync,
  {
    QueryValidator::validate_collection_size(&items, 10000, "batch items")?;

    let mut all_results = Vec::new();
    let chunks: Vec<&[T]> = items.chunks(batch_size).collect();

    info!("Executing batch SELECT with {} chunks of max size {}", chunks.len(), batch_size);

    for (i, chunk) in chunks.iter().enumerate() {
      debug!("Processing batch chunk {} of {}", i + 1, chunks.len());

      let query = query_fn(chunk)?;
      let mut conn = pool.acquire().await.map_err(|e| {
        FriendshipError::DatabaseError(format!("Failed to acquire connection for batch {}: {}", i, e))
      })?;

      let chunk_results = sqlx::query(&query).fetch_all(&mut *conn).await.map_err(|e| {
        FriendshipError::DatabaseError(format!("Batch query {} failed: {}", i, e))
      })?;

      all_results.extend(chunk_results);
    }

    info!("Batch SELECT completed: {} total results from {} chunks", all_results.len(), chunks.len());
    Ok(all_results)
  }

  /// Execute batch INSERT operations with chunking and optional transactions
  pub async fn batch_insert(
    pool: &Pool<MySql>,
    table_name: &str,
    columns: &[&str],
    values: Vec<Vec<String>>,
    config: BatchConfig,
  ) -> FriendshipResult<u64> {
    QueryValidator::validate_collection_size(&values, 50000, "insert values")?;

    if columns.is_empty() {
      return Err(FriendshipError::InvalidInput("Columns cannot be empty".to_string()));
    }

    let mut total_affected = 0u64;
    let chunks: Vec<&[Vec<String>]> = values.chunks(config.max_batch_size).collect();

    info!(
      "Executing batch INSERT into {} with {} chunks of max size {}",
      table_name,
      chunks.len(),
      config.max_batch_size
    );

    for (i, chunk) in chunks.iter().enumerate() {
      debug!("Processing INSERT batch chunk {} of {}", i + 1, chunks.len());

      let affected = if config.use_transactions {
        Self::batch_insert_with_transaction(pool, table_name, columns, chunk).await?
      } else {
        Self::batch_insert_without_transaction(pool, table_name, columns, chunk).await?
      };

      total_affected += affected;
    }

    info!("Batch INSERT completed: {} total rows affected", total_affected);
    Ok(total_affected)
  }

  /// Execute batch INSERT with transaction
  async fn batch_insert_with_transaction(
    pool: &Pool<MySql>,
    table_name: &str,
    columns: &[&str],
    values: &[Vec<String>],
  ) -> FriendshipResult<u64> {
    let mut tx = pool.begin().await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Failed to begin transaction: {}", e))
    })?;

    let query = Self::build_batch_insert_query(table_name, columns, values.len())?;
    let mut query_builder = sqlx::query(&query);

    // Bind all values
    for value_row in values {
      for value in value_row {
        query_builder = query_builder.bind(value);
      }
    }

    let result = query_builder.execute(&mut *tx).await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Batch INSERT failed: {}", e))
    })?;

    tx.commit().await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Failed to commit transaction: {}", e))
    })?;

    Ok(result.rows_affected())
  }

  /// Execute batch INSERT without transaction (faster but less safe)
  async fn batch_insert_without_transaction(
    pool: &Pool<MySql>,
    table_name: &str,
    columns: &[&str],
    values: &[Vec<String>],
  ) -> FriendshipResult<u64> {
    let mut conn = pool.acquire().await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Failed to acquire connection: {}", e))
    })?;

    let query = Self::build_batch_insert_query(table_name, columns, values.len())?;
    let mut query_builder = sqlx::query(&query);

    // Bind all values
    for value_row in values {
      for value in value_row {
        query_builder = query_builder.bind(value);
      }
    }

    let result = query_builder.execute(&mut *conn).await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Batch INSERT failed: {}", e))
    })?;

    Ok(result.rows_affected())
  }

  /// Build batch INSERT query
  fn build_batch_insert_query(
    table_name: &str,
    columns: &[&str],
    row_count: usize,
  ) -> FriendshipResult<String> {
    if row_count == 0 {
      return Err(FriendshipError::InvalidInput("No rows to insert".to_string()));
    }

    let columns_str = columns.join(", ");
    let value_placeholders = columns.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
    let values_clause = (0..row_count)
      .map(|_| format!("({})", value_placeholders))
      .collect::<Vec<_>>()
      .join(", ");

    Ok(format!(
      "INSERT INTO {} ({}) VALUES {}",
      table_name, columns_str, values_clause
    ))
  }

  /// Execute batch UPDATE operations
  pub async fn batch_update(
    pool: &Pool<MySql>,
    table_name: &str,
    updates: Vec<(HashMap<String, String>, HashMap<String, String>)>, // (set_clauses, where_clauses)
    config: BatchConfig,
  ) -> FriendshipResult<u64> {
    QueryValidator::validate_collection_size(&updates, 10000, "update operations")?;

    let mut total_affected = 0u64;
    let chunks: Vec<&[(HashMap<String, String>, HashMap<String, String>)]> =
      updates.chunks(config.max_batch_size).collect();

    info!(
      "Executing batch UPDATE on {} with {} chunks of max size {}",
      table_name,
      chunks.len(),
      config.max_batch_size
    );

    for (i, chunk) in chunks.iter().enumerate() {
      debug!("Processing UPDATE batch chunk {} of {}", i + 1, chunks.len());

      let affected = if config.use_transactions {
        Self::batch_update_with_transaction(pool, table_name, chunk).await?
      } else {
        Self::batch_update_without_transaction(pool, table_name, chunk).await?
      };

      total_affected += affected;
    }

    info!("Batch UPDATE completed: {} total rows affected", total_affected);
    Ok(total_affected)
  }

  /// Execute batch UPDATE with transaction
  async fn batch_update_with_transaction(
    pool: &Pool<MySql>,
    table_name: &str,
    updates: &[(HashMap<String, String>, HashMap<String, String>)],
  ) -> FriendshipResult<u64> {
    let mut tx = pool.begin().await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Failed to begin transaction: {}", e))
    })?;

    let mut total_affected = 0u64;

    for (set_clauses, where_clauses) in updates {
      let query = Self::build_update_query(table_name, set_clauses, where_clauses)?;
      let mut query_builder = sqlx::query(&query);

      // Bind SET values
      for value in set_clauses.values() {
        query_builder = query_builder.bind(value);
      }

      // Bind WHERE values
      for value in where_clauses.values() {
        query_builder = query_builder.bind(value);
      }

      let result = query_builder.execute(&mut *tx).await.map_err(|e| {
        FriendshipError::DatabaseError(format!("UPDATE failed: {}", e))
      })?;

      total_affected += result.rows_affected();
    }

    tx.commit().await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Failed to commit transaction: {}", e))
    })?;

    Ok(total_affected)
  }

  /// Execute batch UPDATE without transaction
  async fn batch_update_without_transaction(
    pool: &Pool<MySql>,
    table_name: &str,
    updates: &[(HashMap<String, String>, HashMap<String, String>)],
  ) -> FriendshipResult<u64> {
    let mut conn = pool.acquire().await.map_err(|e| {
      FriendshipError::DatabaseError(format!("Failed to acquire connection: {}", e))
    })?;

    let mut total_affected = 0u64;

    for (set_clauses, where_clauses) in updates {
      let query = Self::build_update_query(table_name, set_clauses, where_clauses)?;
      let mut query_builder = sqlx::query(&query);

      // Bind SET values
      for value in set_clauses.values() {
        query_builder = query_builder.bind(value);
      }

      // Bind WHERE values
      for value in where_clauses.values() {
        query_builder = query_builder.bind(value);
      }

      let result = query_builder.execute(&mut *conn).await.map_err(|e| {
        FriendshipError::DatabaseError(format!("UPDATE failed: {}", e))
      })?;

      total_affected += result.rows_affected();
    }

    Ok(total_affected)
  }

  /// Build UPDATE query
  fn build_update_query(
    table_name: &str,
    set_clauses: &HashMap<String, String>,
    where_clauses: &HashMap<String, String>,
  ) -> FriendshipResult<String> {
    if set_clauses.is_empty() {
      return Err(FriendshipError::InvalidInput("SET clauses cannot be empty".to_string()));
    }

    if where_clauses.is_empty() {
      return Err(FriendshipError::InvalidInput("WHERE clauses cannot be empty for safety".to_string()));
    }

    let set_clause = set_clauses
      .keys()
      .map(|col| format!("{} = ?", col))
      .collect::<Vec<_>>()
      .join(", ");

    let where_clause = where_clauses
      .keys()
      .map(|col| format!("{} = ?", col))
      .collect::<Vec<_>>()
      .join(" AND ");

    Ok(format!(
      "UPDATE {} SET {} WHERE {}",
      table_name, set_clause, where_clause
    ))
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_build_batch_insert_query() {
    let query = BatchOperations::build_batch_insert_query("users", &["name", "email"], 2).unwrap();
    assert_eq!(query, "INSERT INTO users (name, email) VALUES (?, ?), (?, ?)");
  }

  #[test]
  fn test_build_update_query() {
    let mut set_clauses = HashMap::new();
    set_clauses.insert("name".to_string(), "John".to_string());
    set_clauses.insert("email".to_string(), "<EMAIL>".to_string());

    let mut where_clauses = HashMap::new();
    where_clauses.insert("id".to_string(), "1".to_string());

    let query = BatchOperations::build_update_query("users", &set_clauses, &where_clauses).unwrap();
    // Note: HashMap iteration order is not guaranteed, so we check for both possible orders
    assert!(
      query == "UPDATE users SET name = ?, email = ? WHERE id = ?" ||
      query == "UPDATE users SET email = ?, name = ? WHERE id = ?"
    );
  }

  #[test]
  fn test_batch_config() {
    let default_config = BatchConfig::default();
    assert_eq!(default_config.max_batch_size, 1000);
    assert!(default_config.use_transactions);

    let high_throughput_config = BatchConfig::high_throughput();
    assert_eq!(high_throughput_config.max_batch_size, 5000);
    assert!(!high_throughput_config.use_transactions);
  }
}
