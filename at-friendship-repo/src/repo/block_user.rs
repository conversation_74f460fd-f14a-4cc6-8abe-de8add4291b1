use {crate::repo::po::block_user::BlockedUserPo,
     async_trait::async_trait,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::{Auid,
                                       BlockUserStatus,
                                       BlockedUserEntity,
                                       BlockedUserRepo,
                                       BotimUserId,
                                       FriendshipError,
                                       FriendshipResult},
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::id::uuid::UuidBytes,
     cache::BlockedUserCache,
     sqlx::{FromRow,
            MySql,
            Pool,
            Row},
     tracing::{debug,
               error}};

pub use cache::CacheStats;

pub struct BlockedUserRepoImpl {
  pool:  Pool<MySql>,
  cache: BlockedUserCache,
}

impl BlockedUserRepoImpl {
  pub fn new(pool: Pool<MySql>, redis_pool: fred::clients::Pool) -> Self {
    Self {
      pool,
      cache: BlockedUserCache::new(redis_pool),
    }
  }

  /// Get cache statistics
  pub fn get_cache_stats(&self) -> CacheStats {
    self.cache.get_cache_stats()
  }

  /// Reset cache statistics
  pub fn reset_cache_stats(&self) {
    self.cache.reset_cache_stats()
  }

  /// Print cache statistics
  pub fn print_cache_stats(&self) {
    let stats = self.get_cache_stats();
    println!("=== Blocked User Cache Statistics ===");
    println!("Hit count: {}", stats.hit_count);
    println!("Miss count: {}", stats.miss_count);
    println!("Total count: {}", stats.total_count);
    println!("Hit rate: {:.2}%", stats.hit_rate * 100.0);
    println!("=====================================");
  }

  /// Update cache after save operation, ignore failures
  async fn update_cache_after_save(&self, blocked_user_entity: &BlockedUserEntity) {
    let is_blocked = matches!(blocked_user_entity.status, BlockUserStatus::Blocked);
    let _ = self
      .cache
      .set_blocked_status(
        &blocked_user_entity.user_id,
        &blocked_user_entity.blocked_user_id,
        is_blocked,
      )
      .await;
  }
}

#[async_trait]
impl BlockedUserRepo for BlockedUserRepoImpl {
  #[tracing::instrument(name = "save_blocked_user_entity", skip(self, blocked_user_entity))]
  async fn save_blocked_user_entity(&self, blocked_user_entity: &BlockedUserEntity) -> FriendshipResult<()> {
    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    debug!("Saving blocked user entity: {:?}", blocked_user_entity);

    let query = "INSERT INTO t_blocked_users (user_id, blocked_user_id, status) VALUES (?, ?, ?) ON DUPLICATE KEY \
                 UPDATE status = VALUES(status)";
    debug!("Query: {}", query);

    let query_builder = sqlx::query(query);

    let query_result = query_builder
      .bind(blocked_user_entity.user_id.inner_ref().inner_ref().inner_ref().to_vec())
      .bind(
        blocked_user_entity
          .blocked_user_id
          .inner_ref()
          .inner_ref()
          .inner_ref()
          .to_vec(),
      )
      .bind(blocked_user_entity.status.code())
      .execute(&mut *conn)
      .await
      .map_err(|e| {
        error!("Failed to save blocked user entity: {:?}", e);
        FriendshipError::DatabaseError(e.to_string())
      })?;

    if query_result.rows_affected() == 0 {
      error!(
        "Failed to save blocked user entity: {:?}",
        FriendshipError::DatabaseError("Failed to save blocked user entity".to_string())
      );
      return Err(FriendshipError::DatabaseError(
        "Failed to save blocked user entity".to_string(),
      ));
    }

    // Update cache after successful database save
    self.update_cache_after_save(blocked_user_entity).await;

    Ok(())
  }

  #[tracing::instrument(name = "get_blocked_user_entities", skip(self, user_id))]
  async fn get_blocked_user_entities(&self, user_id: &BotimUserId) -> FriendshipResult<Vec<BlockedUserEntity>> {
    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let query = "SELECT user_id, blocked_user_id, status, created_at, updated_at FROM t_blocked_users WHERE user_id = \
                 ? and status = 1";
    let query_builder = sqlx::query(query);
    let query_result = query_builder
      .bind(user_id.inner_ref().inner_ref().inner_ref().to_vec())
      .fetch_all(&mut *conn)
      .await
      .map_err(|e| {
        error!("Failed to get blocked user entities: {:?}", e);
        FriendshipError::DatabaseError(e.to_string())
      })?;

    // convert the query result to blocked user entities
    let blocked_user_entities = query_result
      .into_iter()
      .map(|row| {
        BlockedUserPo::from_row(&row)
          .map_err(|e| {
            error!("Failed to convert blocked user entity: {:?}", e);
            FriendshipError::DatabaseError(e.to_string())
          })
          .and_then(|po| po.to_blocked_user_entity())
      })
      .collect::<Vec<_>>();

    debug!("Blocked user entities: {}", blocked_user_entities.len());

    Ok(
      blocked_user_entities
        .into_iter()
        .filter_map(|e| match e {
          Ok(e) => Some(e),
          Err(e) => {
            error!("Failed to convert blocked user entity: {:?}", e);
            None
          }
        })
        .collect(),
    )
  }

  #[tracing::instrument(name = "is_blocked", skip(self, user_id, blocked_user_id))]
  async fn is_blocked(&self, user_id: &BotimUserId, blocked_user_id: &BotimUserId) -> FriendshipResult<bool> {
    // Try cache first, fallback to database on miss or error
    if let Ok(Some(cached_result)) = self.cache.get_blocked_status(user_id, blocked_user_id).await {
      return Ok(cached_result);
    }

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let query = "SELECT COUNT(*) FROM t_blocked_users WHERE user_id = ? AND blocked_user_id = ? AND status = 1";
    let query_result = sqlx::query(query)
      .bind(user_id.inner_ref().inner_ref().inner_ref().to_vec())
      .bind(blocked_user_id.inner_ref().inner_ref().inner_ref().to_vec())
      .fetch_one(&mut *conn)
      .await
      .map_err(|e| {
        error!(
          "Failed to check if user {} is blocked by {}: {:?}",
          user_id, blocked_user_id, e
        );
        FriendshipError::DatabaseError(e.to_string())
      })?;

    let count = query_result.get::<i64, _>(0);
    let is_blocked = count > 0;

    // Cache result, ignore failures
    let _ = self
      .cache
      .set_blocked_status(user_id, blocked_user_id, is_blocked)
      .await;

    Ok(is_blocked)
  }

  async fn is_blocked_by_users(
    &self,
    user_ids: &[BotimUserId],
    blocked_user_id: &BotimUserId,
  ) -> FriendshipResult<Vec<BotimUserId>> {
    if user_ids.is_empty() {
      return Ok(vec![]);
    }

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    // Validate input size to prevent excessive parameter binding
    if user_ids.len() > 1000 {
      return Err(FriendshipError::InvalidInput(
        "Too many user IDs in single query (max 1000)".to_string(),
      ));
    }

    let placeholders = user_ids.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
    let query = format!(
      "SELECT user_id FROM t_blocked_users WHERE user_id IN ({}) AND blocked_user_id = ? AND status = 1",
      placeholders
    );

    let query_builder = user_ids.iter().fold(sqlx::query(&query), |builder, user_id| {
      builder.bind(user_id.inner_ref().inner_ref().inner_ref().to_vec())
    });

    let query_result = query_builder
      .bind(blocked_user_id.inner_ref().inner_ref().inner_ref().to_vec())
      .fetch_all(&mut *conn)
      .await
      .map_err(|e| {
        error!(
          "Failed to check if users {:?} are blocked by {}: {:?}",
          user_ids, blocked_user_id, e
        );
        FriendshipError::DatabaseError(e.to_string())
      })?;

    let block_user_ids = query_result
      .into_iter()
      .filter_map(|row| {
        let user_id: Vec<u8> = row.get(0);
        let uuid_array: Result<[u8; 16], _> = user_id.as_slice().try_into();
        match uuid_array {
          Ok(bytes) => Some(BotimUserId::new(Auid::new(UuidBytes::new(bytes)))),
          Err(_) => None,
        }
      })
      .collect::<Vec<_>>();

    // Note: We don't cache bulk query results

    Ok(block_user_ids)
  }
}

mod cache;

// add a test for the get_blocked_user_entities function
#[cfg(test)]
mod tests {
  use {super::*,
       at_friendship_domain_model::all::{Auid,
                                         BlockUserStatus,
                                         BlockedUserEntity,
                                         BotimUserId},
       at_lib_comm_domain::{id::uuid::UuidBytes,
                            value::time::{CreateAt,
                                          UpdateAt}},
       at_otel_integration::prelude::*,
       chrono::Utc,
       sqlx::mysql::MySqlPoolOptions,
       std::sync::Once,
       tracing::info};

  static INIT: Once = Once::new();

  async fn create_test_pool() -> Pool<MySql> {
    INIT.call_once(|| {
      let log_config = LogConfigDTO {
        name:           "at-friendship-repo".to_string(),
        output:         "console://stdout".to_string(),
        log_level:      Some("debug".to_string()),
        log_level_list: None,
        line_jsonfy:    None,
      };
      let tracing_config = TracingConfigDTO {
        endpoint:                    "http://localhost:4317".to_string(),
        service_name:                Some("at-friendship-repo-test".to_string()),
        service_version:             Some("1.0.0".to_string()),
        organization:                None,
        stream_name:                 None,
        deployment_environment:      None,
        headers:                     std::collections::HashMap::new(),
        periodic_read_interval_secs: 60,
        trace_filter:                None,
      };
      let log_initializer = OtelLogAdapter::new();
      let _ = log_initializer.init_tracing(&[log_config], &tracing_config);
    });

    let db_url = "mysql://friendshipuser:friendship_pwpPKDushT1h3J@***********:4000/friendship".to_string();
    info!("Using database URL for test: {}", db_url);

    // Create and return connection pool
    MySqlPoolOptions::new()
      .max_connections(5)
      .connect(&db_url)
      .await
      .expect("Failed to create test database pool")
  }

  // Create shared test redis pool
  async fn create_test_redis_pool() -> fred::clients::Pool {
    use fred::interfaces::ClientLike;

    let redis_url = "redis://127.0.0.1:6379";
    let redis_config = fred::types::config::Config::from_url(redis_url).expect("Failed to parse redis url");
    let redis_pool = fred::clients::Pool::new(redis_config, None, None, None, 2).expect("Failed to create redis pool");
    redis_pool.connect_pool();
    redis_pool.wait_for_connect().await.expect("Failed to connect to redis");
    redis_pool
  }

  // Create test repo with short TTL for cache tests
  async fn create_test_repo_with_short_ttl() -> BlockedUserRepoImpl {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let cache = BlockedUserCache::new_with_ttl(redis_pool, 2); // 2 seconds TTL

    BlockedUserRepoImpl { pool, cache }
  }

  #[tokio::test]
  // create a test for the save blocked user entity function
  async fn test_save_blocked_user_entity() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BlockedUserRepoImpl::new(pool, redis_pool);

    let blocked_user_entity = BlockedUserEntity {
      user_id:         BotimUserId::new(Auid::new(UuidBytes::generate())),
      blocked_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
      status:          BlockUserStatus::Blocked,
      created_at:      CreateAt::new(Utc::now()),
      updated_at:      UpdateAt::new(Utc::now()),
    };
    let result = repo.save_blocked_user_entity(&blocked_user_entity).await;
    assert!(result.is_ok());
    let blocked_user_entities = repo.get_blocked_user_entities(&blocked_user_entity.user_id).await;
    assert!(blocked_user_entities.is_ok());
    let entities = blocked_user_entities.unwrap();
    debug!("Entities: {:?}", entities);
    assert_eq!(entities.len(), 1);
    assert_eq!(entities[0].status, BlockUserStatus::Blocked);

    let blocked_user_entity = BlockedUserEntity {
      user_id:         blocked_user_entity.user_id,
      blocked_user_id: blocked_user_entity.blocked_user_id,
      status:          BlockUserStatus::Unblocked,
      created_at:      CreateAt::new(Utc::now()),
      updated_at:      UpdateAt::new(Utc::now()),
    };
    let result = repo.save_blocked_user_entity(&blocked_user_entity).await;
    assert!(result.is_ok());
    let blocked_user_entities = repo.get_blocked_user_entities(&blocked_user_entity.user_id).await;
    assert!(blocked_user_entities.is_ok());
    let entities = blocked_user_entities.unwrap();
    assert_eq!(entities.len(), 0);
  }

  #[tokio::test]
  async fn test_is_blocked() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BlockedUserRepoImpl::new(pool, redis_pool);
    // save a blocked user
    let blocked_user_entity = BlockedUserEntity {
      user_id:         BotimUserId::new(Auid::new(UuidBytes::generate())),
      blocked_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
      status:          BlockUserStatus::Blocked,
      created_at:      CreateAt::new(Utc::now()),
      updated_at:      UpdateAt::new(Utc::now()),
    };
    let result = repo.save_blocked_user_entity(&blocked_user_entity).await;
    assert!(result.is_ok());
    // check if the user is blocked
    let is_blocked = repo
      .is_blocked(&blocked_user_entity.user_id, &blocked_user_entity.blocked_user_id)
      .await;
    assert!(is_blocked.is_ok());
    assert!(is_blocked.unwrap());
  }

  #[tokio::test]
  async fn test_is_blocked_cache_functionality() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BlockedUserRepoImpl::new(pool, redis_pool);

    // Reset cache statistics
    repo.reset_cache_stats();

    // Create test data
    let blocked_user_entity = BlockedUserEntity {
      user_id:         BotimUserId::new(Auid::new(UuidBytes::generate())),
      blocked_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
      status:          BlockUserStatus::Blocked,
      created_at:      CreateAt::new(Utc::now()),
      updated_at:      UpdateAt::new(Utc::now()),
    };

    // Save blocked user - this should cache the result
    let result = repo.save_blocked_user_entity(&blocked_user_entity).await;
    assert!(result.is_ok());

    // Reset cache statistics to test query behavior
    repo.reset_cache_stats();

    // First query - should be cache hit (because save already cached the result)
    let is_blocked_1 = repo
      .is_blocked(&blocked_user_entity.user_id, &blocked_user_entity.blocked_user_id)
      .await;
    assert!(is_blocked_1.is_ok());
    assert!(is_blocked_1.unwrap());

    // Second query - should be cache hit
    let is_blocked_2 = repo
      .is_blocked(&blocked_user_entity.user_id, &blocked_user_entity.blocked_user_id)
      .await;
    assert!(is_blocked_2.is_ok());
    assert!(is_blocked_2.unwrap());

    // Third query - should be cache hit
    let is_blocked_3 = repo
      .is_blocked(&blocked_user_entity.user_id, &blocked_user_entity.blocked_user_id)
      .await;
    assert!(is_blocked_3.is_ok());
    assert!(is_blocked_3.unwrap());

    // Get cache statistics
    let stats = repo.get_cache_stats();

    println!("Cache statistics:");
    println!("  Hit count: {}", stats.hit_count);
    println!("  Miss count: {}", stats.miss_count);
    println!("  Total count: {}", stats.total_count);
    println!("  Hit rate: {:.2}%", stats.hit_rate * 100.0);

    // Verify statistics - All queries should hit cache since save already cached the data
    assert_eq!(stats.hit_count, 3); // All queries should hit cache
    assert_eq!(stats.miss_count, 0); // No cache misses
    assert_eq!(stats.total_count, 3); // Total 3 queries
    assert!((stats.hit_rate - 1.0).abs() < 0.001); // Hit rate should be 100%
  }

  #[tokio::test]
  async fn test_cache_expiration_with_short_ttl() {
    let repo = create_test_repo_with_short_ttl().await;

    // Reset cache statistics
    repo.reset_cache_stats();

    // Create test data
    let blocked_user_entity = BlockedUserEntity {
      user_id:         BotimUserId::new(Auid::new(UuidBytes::generate())),
      blocked_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
      status:          BlockUserStatus::Blocked,
      created_at:      CreateAt::new(Utc::now()),
      updated_at:      UpdateAt::new(Utc::now()),
    };

    // Save blocked user - this should cache the result
    let result = repo.save_blocked_user_entity(&blocked_user_entity).await;
    assert!(result.is_ok());

    // Reset cache statistics to test query behavior
    repo.reset_cache_stats();

    // First query - should be cache hit (because save already cached the result)
    let is_blocked_1 = repo
      .is_blocked(&blocked_user_entity.user_id, &blocked_user_entity.blocked_user_id)
      .await;
    assert!(is_blocked_1.is_ok());
    assert!(is_blocked_1.unwrap());

    // Second query - should be cache hit
    let is_blocked_2 = repo
      .is_blocked(&blocked_user_entity.user_id, &blocked_user_entity.blocked_user_id)
      .await;
    assert!(is_blocked_2.is_ok());
    assert!(is_blocked_2.unwrap());

    // Wait for cache to expire (TTL is 2 seconds)
    tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

    // Third query - should be cache miss because cache expired
    let is_blocked_3 = repo
      .is_blocked(&blocked_user_entity.user_id, &blocked_user_entity.blocked_user_id)
      .await;
    assert!(is_blocked_3.is_ok());
    assert!(is_blocked_3.unwrap());

    // Get cache statistics
    let stats = repo.get_cache_stats();

    println!("Cache expiration test statistics:");
    println!("  Hit count: {}", stats.hit_count);
    println!("  Miss count: {}", stats.miss_count);
    println!("  Total count: {}", stats.total_count);
    println!("  Hit rate: {:.2}%", stats.hit_rate * 100.0);

    // Verify statistics - First two queries hit cache, third query misses after expiration
    assert_eq!(stats.hit_count, 2); // First and second queries should hit cache
    assert_eq!(stats.miss_count, 1); // Third query should miss cache due to expiration
    assert_eq!(stats.total_count, 3); // Total 3 queries
    assert!((stats.hit_rate - 0.6667).abs() < 0.001); // Hit rate should be about 66.67%
  }

  #[tokio::test]
  async fn test_is_blocked_by_users() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BlockedUserRepoImpl::new(pool, redis_pool);

    // Create test users
    let blocked_user_id = BotimUserId::new(Auid::new(UuidBytes::generate()));
    let blocker_user_1 = BotimUserId::new(Auid::new(UuidBytes::generate()));
    let blocker_user_2 = BotimUserId::new(Auid::new(UuidBytes::generate()));
    let non_blocker_user = BotimUserId::new(Auid::new(UuidBytes::generate()));

    // Save blocked user entities - blocker_user_1 and blocker_user_2 block blocked_user_id
    let blocked_entity_1 = BlockedUserEntity {
      user_id:         blocker_user_1.clone(),
      blocked_user_id: blocked_user_id.clone(),
      status:          BlockUserStatus::Blocked,
      created_at:      CreateAt::new(Utc::now()),
      updated_at:      UpdateAt::new(Utc::now()),
    };

    let blocked_entity_2 = BlockedUserEntity {
      user_id:         blocker_user_2.clone(),
      blocked_user_id: blocked_user_id.clone(),
      status:          BlockUserStatus::Blocked,
      created_at:      CreateAt::new(Utc::now()),
      updated_at:      UpdateAt::new(Utc::now()),
    };

    // Save the blocked user entities
    let result1 = repo.save_blocked_user_entity(&blocked_entity_1).await;
    assert!(result1.is_ok(), "Failed to save first blocked user entity");

    let result2 = repo.save_blocked_user_entity(&blocked_entity_2).await;
    assert!(result2.is_ok(), "Failed to save second blocked user entity");

    // Test is_blocked_by_users with a mix of blocking and non-blocking users
    let test_user_ids = vec![blocker_user_1.clone(), blocker_user_2.clone(), non_blocker_user.clone()];

    let result = repo.is_blocked_by_users(&test_user_ids, &blocked_user_id).await;
    assert!(result.is_ok(), "is_blocked_by_users should succeed");

    let blocking_users = result.unwrap();
    debug!("Blocking users: {:?}", blocking_users);

    // Should find exactly 2 users who blocked the target user
    assert_eq!(blocking_users.len(), 2, "Expected exactly 2 blocking users");
    assert!(
      blocking_users.contains(&blocker_user_1),
      "Should contain blocker_user_1"
    );
    assert!(
      blocking_users.contains(&blocker_user_2),
      "Should contain blocker_user_2"
    );
    assert!(
      !blocking_users.contains(&non_blocker_user),
      "Should not contain non_blocker_user"
    );

    // Test with empty user list
    let empty_result = repo.is_blocked_by_users(&[], &blocked_user_id).await;
    assert!(empty_result.is_ok(), "Empty user list should succeed");
    assert_eq!(
      empty_result.unwrap().len(),
      0,
      "Empty user list should return empty result"
    );

    // Test with non-existent blocked user
    let non_existent_blocked_user = BotimUserId::new(Auid::new(UuidBytes::generate()));
    let no_block_result = repo
      .is_blocked_by_users(&test_user_ids, &non_existent_blocked_user)
      .await;
    assert!(
      no_block_result.is_ok(),
      "Query for non-existent blocked user should succeed"
    );
    assert_eq!(
      no_block_result.unwrap().len(),
      0,
      "Should find no blocking users for non-existent blocked user"
    );

    println!("✅ is_blocked_by_users test completed successfully");
  }
}
