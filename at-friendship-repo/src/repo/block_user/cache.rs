use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     fred::prelude::*,
     hex,
     serde::{Deserialize,
             Serialize},
     serde_json,
     std::{collections::hash_map::Default<PERSON>asher,
           hash::{<PERSON>h,
                  <PERSON><PERSON>},
           sync::atomic::{AtomicU64,
                          Ordering}},
     tracing::{debug,
               error}};

/// Cache data structure for blocked user status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub(super) struct CachedBlockedStatus {
  pub user_id:         String,
  pub blocked_user_id: String,
  pub is_blocked:      bool,
  pub cached_at:       i64,
}

impl CachedBlockedStatus {
  pub fn new(user_id: &BotimUserId, blocked_user_id: &BotimUserId, is_blocked: bool) -> Self {
    Self {
      user_id: hex::encode(user_id.inner_ref().inner_ref().inner_ref()),
      blocked_user_id: hex::encode(blocked_user_id.inner_ref().inner_ref().inner_ref()),
      is_blocked,
      cached_at: chrono::Utc::now().timestamp_millis(),
    }
  }
}

/// Cache statistics information
#[derive(Debug, Clone)]
pub struct CacheStats {
  pub hit_count:   u64,
  pub miss_count:  u64,
  pub total_count: u64,
  pub hit_rate:    f64,
}

/// BlockedUser cache manager
pub(super) struct BlockedUserCache {
  redis_pool:  fred::clients::Pool,
  hit_count:   AtomicU64,
  miss_count:  AtomicU64,
  ttl_seconds: u64,
}

impl BlockedUserCache {
  pub fn new(redis_pool: fred::clients::Pool) -> Self {
    Self {
      redis_pool,
      hit_count: AtomicU64::new(0),
      miss_count: AtomicU64::new(0),
      ttl_seconds: 300, // Default 5 minutes
    }
  }

  #[allow(dead_code)]
  pub(super) fn new_with_ttl(redis_pool: fred::clients::Pool, ttl_seconds: u64) -> Self {
    Self {
      redis_pool,
      hit_count: AtomicU64::new(0),
      miss_count: AtomicU64::new(0),
      ttl_seconds,
    }
  }

  // Generate cache key for blocked user pair
  fn generate_cache_key(&self, user_id: &BotimUserId, blocked_user_id: &BotimUserId) -> String {
    let mut hasher = DefaultHasher::new();
    let key_data = format!(
      "{}:{}",
      hex::encode(user_id.inner_ref().inner_ref().inner_ref()),
      hex::encode(blocked_user_id.inner_ref().inner_ref().inner_ref())
    );
    key_data.hash(&mut hasher);

    format!("blocked_user:is_blocked:{:x}", hasher.finish())
  }

  // Get blocked status from cache
  #[tracing::instrument(name = "get_blocked_status", skip(self, user_id, blocked_user_id))]
  pub async fn get_blocked_status(
    &self,
    user_id: &BotimUserId,
    blocked_user_id: &BotimUserId,
  ) -> FriendshipResult<Option<bool>> {
    let cache_key = self.generate_cache_key(user_id, blocked_user_id);

    let cached_json: Option<String> = self
      .redis_pool
      .get::<Option<String>, _>(&cache_key)
      .await
      .map_err(|e| {
        error!("Failed to get blocked status from cache: {:?}", e);
        FriendshipError::RedisError(e.to_string())
      })?;

    match cached_json {
      Some(json) => {
        // Cache hit
        self.hit_count.fetch_add(1, Ordering::Relaxed);

        let cached_status: CachedBlockedStatus = serde_json::from_str(&json).map_err(|e| {
          error!("Failed to deserialize cached blocked status: {:?}", e);
          FriendshipError::Deserialization(e.to_string())
        })?;

        debug!("Cache hit for blocked status cache key: {}", cache_key);
        Ok(Some(cached_status.is_blocked))
      }
      None => {
        // Cache miss
        self.miss_count.fetch_add(1, Ordering::Relaxed);
        debug!("Cache miss for blocked status cache key: {}", cache_key);
        Ok(None)
      }
    }
  }

  // Set blocked status in cache
  #[tracing::instrument(name = "set_blocked_status", skip(self, user_id, blocked_user_id, is_blocked))]
  pub async fn set_blocked_status(
    &self,
    user_id: &BotimUserId,
    blocked_user_id: &BotimUserId,
    is_blocked: bool,
  ) -> FriendshipResult<()> {
    let cache_key = self.generate_cache_key(user_id, blocked_user_id);
    let cached_status = CachedBlockedStatus::new(user_id, blocked_user_id, is_blocked);

    let json = serde_json::to_string(&cached_status).map_err(|e| {
      error!("Failed to serialize blocked status for cache: {:?}", e);
      FriendshipError::Deserialization(e.to_string())
    })?;

    // Set cache with configurable expiration
    let ttl = Expiration::EX(self.ttl_seconds as i64);
    self
      .redis_pool
      .set::<(), _, _>(&cache_key, json, Some(ttl), None, false)
      .await
      .map_err(|e| {
        error!("Failed to set blocked status to cache: {:?}", e);
        FriendshipError::RedisError(e.to_string())
      })?;

    debug!("Cached blocked status for key: {}", cache_key);
    Ok(())
  }

  /// Get cache statistics
  pub fn get_cache_stats(&self) -> CacheStats {
    let hit_count = self.hit_count.load(Ordering::Relaxed);
    let miss_count = self.miss_count.load(Ordering::Relaxed);
    let total_count = hit_count + miss_count;

    let hit_rate = if total_count > 0 {
      hit_count as f64 / total_count as f64
    } else {
      0.0
    };

    CacheStats {
      hit_count,
      miss_count,
      total_count,
      hit_rate,
    }
  }

  /// Reset cache statistics
  pub fn reset_cache_stats(&self) {
    self.hit_count.store(0, Ordering::Relaxed);
    self.miss_count.store(0, Ordering::Relaxed);
  }
}
