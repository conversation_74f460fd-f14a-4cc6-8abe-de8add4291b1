use {crate::repo::po::botim_user::<PERSON><PERSON>m<PERSON>serPo,
     async_trait::async_trait,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     cache::BotimUserCache,
     sqlx::{FromRow,
            MySql,
            Pool},
     tracing::{debug,
               error}};

pub use cache::CacheStats;

pub struct BotimUserRepoImpl {
  pool:  Pool<MySql>,
  cache: BotimUserCache,
}

impl BotimUserRepoImpl {
  pub fn new(pool: Pool<MySql>, redis_pool: fred::clients::Pool) -> Self {
    Self {
      pool,
      cache: BotimUserCache::new(redis_pool),
    }
  }

  /// Get cache statistics
  pub fn get_cache_stats(&self) -> CacheStats {
    self.cache.get_cache_stats()
  }

  /// Reset cache statistics
  pub fn reset_cache_stats(&self) {
    self.cache.reset_cache_stats()
  }

  /// Print cache statistics
  pub fn print_cache_stats(&self) {
    let stats = self.get_cache_stats();
    println!("=== Cache Statistics ===");
    println!("Hit count: {}", stats.hit_count);
    println!("Miss count: {}", stats.miss_count);
    println!("Total count: {}", stats.total_count);
    println!("Hit rate: {:.2}%", stats.hit_rate * 100.0);
    println!("=======================");
  }

  /// Update cache after successful database save, ignore failures
  async fn update_cache_after_save(&self, botim_user_entities: &[BotimUserEntity]) {
    let phone_numbers: Vec<PhoneNumber> = botim_user_entities.iter().map(|e| e.phone_number.clone()).collect();
    let _ = self
      .cache
      .set_by_phone_numbers(&phone_numbers, botim_user_entities)
      .await;
  }
}

#[async_trait]
impl BotimUserRepo for BotimUserRepoImpl {
  #[tracing::instrument(name = "save_botim_user_entities_idempotently", skip(self, botim_user_entities))]
  async fn save_botim_user_entities_idempotently(
    &self,
    botim_user_entities: &[BotimUserEntity],
  ) -> FriendshipResult<()> {
    if botim_user_entities.is_empty() {
      debug!("No botim user entities to save, skipping");
      return Ok(());
    }

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    // Build the SQL query with placeholders
    let placeholders = (0 .. botim_user_entities.len())
      .map(|_| "(?, ?, ?, ?)")
      .collect::<Vec<_>>()
      .join(", ");
    let query = format!(
      "INSERT INTO t_botim_users (botim_user_id, auid_repr, phone_number, updated_at) VALUES {placeholders} ON \
       DUPLICATE KEY UPDATE updated_at = VALUES(updated_at), auid_repr = VALUES(auid_repr), phone_number = \
       VALUES(phone_number)"
    );

    // Bind parameters to the query
    let query_builder = botim_user_entities
      .iter()
      .fold(sqlx::query(&query), |builder, botim_user_entity| {
        builder
          .bind(
            botim_user_entity
              .botim_user_id
              .inner_ref()
              .inner_ref()
              .inner_ref()
              .to_vec(),
          )
          .bind(botim_user_entity.auid_repr.inner_ref().to_string())
          .bind(*botim_user_entity.phone_number.inner_ref())
          .bind(botim_user_entity.updated_at.inner_ref())
      });

    debug!("Executing query: {}", query);

    let query_result = query_builder.execute(&mut *conn).await.map_err(|e| {
      debug!("Error executing query: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    if query_result.rows_affected() == 0 {
      error!(
        "Failed to save botim user entities for {} entities",
        botim_user_entities.len()
      );
      return Err(FriendshipError::DatabaseError(sqlx::Error::RowNotFound.to_string()));
    }

    // Update cache after successful database save
    self.update_cache_after_save(botim_user_entities).await;

    Ok(())
  }

  #[tracing::instrument(name = "get_auids_by_phone_numbers", skip(self, phone_number_filters))]
  async fn get_auids_by_phone_numbers(
    &self,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<BotimUserEntity>> {
    if phone_number_filters.is_empty() {
      debug!("No phone number filters provided, returning empty result");
      return Ok(Vec::new());
    }

    // Try to get from cache first, fallback to database on miss or error
    if let Ok(Some(cached_data)) = self.cache.get_by_phone_numbers(phone_number_filters).await {
      debug!("Cache hit for get_auids_by_phone_numbers");
      return Ok(cached_data);
    }

    debug!("Cache miss or error, querying database for phone numbers");

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let placeholders = phone_number_filters.iter().map(|_| "?").collect::<Vec<_>>().join(", ");

    let query = format!(
      "SELECT botim_user_id, auid_repr, phone_number, created_at, updated_at FROM t_botim_users WHERE phone_number IN \
       ({placeholders})"
    );

    // Use functional chaining
    let result = phone_number_filters
      .iter()
      .fold(sqlx::query(&query), |builder, phone_number| {
        builder.bind(*phone_number.inner_ref())
      })
      .fetch_all(&mut *conn)
      .await
      .map_err(|e| {
        error!("Failed to get auids by phone numbers: {:?}", e);
        FriendshipError::DatabaseError(e.to_string())
      })
      .map(|rows| {
        // Convert query results to entity objects
        rows
          .into_iter()
          .map(|row| {
            BotimUserPo::from_row(&row)
              .map_err(|e| {
                error!("Failed to convert row to botim user PO: {:?}", e);
                FriendshipError::DatabaseError(e.to_string())
              })
              .and_then(|po| po.to_botim_user_entity())
          })
          .filter_map(Result::ok)
          .collect::<Vec<_>>()
      });

    // Cache result, ignore failures
    if let Ok(ref entities) = result {
      let _ = self.cache.set_by_phone_numbers(phone_number_filters, entities).await;
    }

    result
  }

  #[tracing::instrument(name = "delete_botim_user", skip(self, botim_user_id))]
  async fn delete_botim_user(&self, botim_user_id: &BotimUserId) -> FriendshipResult<()> {
    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let query = "DELETE FROM t_botim_users WHERE botim_user_id = ?";
    let query_builder = sqlx::query(query);
    let query_result = query_builder
      .bind(botim_user_id.inner_ref().inner_ref().inner_ref().to_vec())
      .execute(&mut *conn)
      .await
      .map_err(|e| {
        error!("Failed to delete botim user: {:?}", e);
        FriendshipError::DatabaseError(e.to_string())
      })?;

    if query_result.rows_affected() == 0 {
      error!("Failed to delete botim user: {:?}", botim_user_id);
      return Err(FriendshipError::DatabaseError(sqlx::Error::RowNotFound.to_string()));
    }

    Ok(())
  }

  #[tracing::instrument(name = "get_botim_user_entities_by_auids", skip(self, botim_user_ids))]
  async fn get_botim_user_entities_by_auids(
    &self,
    botim_user_ids: &[BotimUserId],
  ) -> FriendshipResult<Vec<BotimUserEntity>> {
    if botim_user_ids.is_empty() {
      debug!("No auids provided, returning empty result");
      return Ok(Vec::new());
    }

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let placeholders = botim_user_ids.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
    let query = format!(
      "SELECT botim_user_id, auid_repr, phone_number, created_at, updated_at FROM t_botim_users WHERE botim_user_id \
       IN ({placeholders})"
    );

    // Use functional chaining
    botim_user_ids
      .iter()
      .fold(sqlx::query(&query), |builder, botim_user_id| {
        builder.bind(botim_user_id.inner_ref().inner_ref().inner_ref().to_vec())
      })
      .fetch_all(&mut *conn)
      .await
      .map_err(|e| {
        error!("Failed to get botim user entities by auids: {:?}", e);
        FriendshipError::DatabaseError(e.to_string())
      })
      .map(|rows| {
        // Convert query results to entity objects
        rows
          .into_iter()
          .map(|row| {
            BotimUserPo::from_row(&row)
              .map_err(|e| {
                error!("Failed to convert row to botim user PO: {:?}", e);
                FriendshipError::DatabaseError(e.to_string())
              })
              .and_then(|po| po.to_botim_user_entity())
          })
          .filter_map(Result::ok)
          .collect::<Vec<_>>()
      })
  }
}

mod cache;

#[cfg(test)]
mod tests {
  use {at_deps::at_friendship::*,
       at_friendship_domain_model::all::*,
       at_internal_deps::at_friendship::*,
       at_lib_comm_domain::{id::uuid::UuidBytes,
                            value::time::{CreateAt,
                                          UpdateAt}},
       chrono::Utc,
       sqlx::mysql::MySqlPoolOptions};

  use super::*;

  // Create shared test database connection function
  async fn create_test_pool() -> Pool<MySql> {
    let db_url = "mysql://friendshipuser:friendship_pwpPKDushT1h3J@***********:4000/friendship";
    // Create and return connection pool
    MySqlPoolOptions::new()
      .max_connections(5)
      .connect(db_url)
      .await
      .expect("Failed to create test database pool")
  }

  // Create shared test redis pool
  async fn create_test_redis_pool() -> fred::clients::Pool {
    use fred::interfaces::ClientLike;

    let redis_url = "redis://127.0.0.1:6379";
    let redis_config = fred::types::config::Config::from_url(redis_url).expect("Failed to parse redis url");
    let redis_pool = fred::clients::Pool::new(redis_config, None, None, None, 2).expect("Failed to create redis pool");
    redis_pool.connect_pool();
    redis_pool.wait_for_connect().await.expect("Failed to connect to redis");
    redis_pool
  }

  // Create test botim user entities
  fn create_test_botim_user_entities() -> Vec<BotimUserEntity> {
    // Use the last 8 digits of nanosecond timestamp as a random suffix to guarantee unique phone numbers in concurrent tests
    let nanos = std::time::SystemTime::now()
      .duration_since(std::time::UNIX_EPOCH)
      .unwrap()
      .as_nanos();
    let suffix = (nanos % 100_000_000) as u64; // 8 digits

    let phone1 = PhoneNumber::new(13800138000u64 + suffix);
    let phone2 = PhoneNumber::new(13900139000u64 + suffix);

    vec![
      BotimUserEntity {
        botim_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
        auid_repr:     AuidRepr::new("1234567890".to_string()),
        phone_number:  phone1,
        created_at:    CreateAt::new(Utc::now()),
        updated_at:    UpdateAt::new(Utc::now()),
      },
      BotimUserEntity {
        botim_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
        auid_repr:     AuidRepr::new("1234567890".to_string()),
        phone_number:  phone2,
        created_at:    CreateAt::new(Utc::now()),
        updated_at:    UpdateAt::new(Utc::now()),
      },
    ]
  }

  // create a test botim user entity with empty vector
  fn create_test_botim_user_entity_with_empty_vector() -> Vec<BotimUserEntity> {
    vec![]
  }

  #[tokio::test]
  async fn test_update_contacts() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;

    // Prepare test data
    let botim_user_entities = create_test_botim_user_entities();

    let repo = BotimUserRepoImpl::new(pool, redis_pool);
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .unwrap();
  }

  #[tokio::test]
  async fn test_save_botim_user_entities_idempotently_with_empty_vector() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    debug!("Created test pool for empty vector test");

    // Prepare test data
    let botim_user_entities = create_test_botim_user_entity_with_empty_vector();
    debug!("Created empty botim user entities vector: {:?}", botim_user_entities);

    let repo = BotimUserRepoImpl::new(pool, redis_pool);
    debug!("About to call save_botim_user_entities_idempotently with empty vector (first time)");

    match repo.save_botim_user_entities_idempotently(&botim_user_entities).await {
      Ok(_) => debug!("First call succeeded as expected"),
      Err(e) => {
        error!("First call failed unexpectedly: {:?}", e);
        panic!("Test failed on first call: {e:?}");
      }
    }

    debug!("About to call save_botim_user_entities_idempotently with empty vector (second time)");
    match repo.save_botim_user_entities_idempotently(&botim_user_entities).await {
      Ok(_) => debug!("Second call succeeded as expected"),
      Err(e) => {
        error!("Second call failed unexpectedly: {:?}", e);
        panic!("Test failed on second call: {e:?}");
      }
    }

    debug!("Test completed successfully");
  }

  #[tokio::test]
  async fn test_get_auids_by_phone_numbers() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;

    // save the botim user entities
    let botim_user_entities = create_test_botim_user_entities();
    let botim_user_id = botim_user_entities[0].botim_user_id.clone();
    let botim_user_id_2 = botim_user_entities[1].botim_user_id.clone();
    let repo = BotimUserRepoImpl::new(pool, redis_pool);
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .unwrap();

    // Prepare query data: use the randomly generated phone numbers saved above
    let phone_num_1 = botim_user_entities[0].phone_number.clone();
    let phone_num_2 = botim_user_entities[1].phone_number.clone();

    let phone_number_filters = vec![phone_num_1.clone()];
    let botim_user_entities = repo.get_auids_by_phone_numbers(&phone_number_filters).await.unwrap();

    botim_user_entities
      .iter()
      .filter(|m| m.botim_user_id == botim_user_id)
      .for_each(|m| {
        assert_eq!(
          m.phone_number.inner_ref().to_string(),
          phone_num_1.inner_ref().to_string()
        );
      });

    let phone_number_filters = vec![phone_num_2.clone()];
    let botim_user_entities = repo.get_auids_by_phone_numbers(&phone_number_filters).await.unwrap();
    botim_user_entities
      .iter()
      .filter(|m| m.botim_user_id == botim_user_id_2)
      .for_each(|m| {
        assert_eq!(
          m.phone_number.inner_ref().to_string(),
          phone_num_2.inner_ref().to_string()
        );
      });
  }

  #[tokio::test]
  async fn test_delete_botim_user() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;

    // save the botim user entities
    let botim_user_entities = create_test_botim_user_entities();
    let botim_user_id = botim_user_entities[0].botim_user_id.clone();
    let repo = BotimUserRepoImpl::new(pool, redis_pool);
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .unwrap();

    // delete the botim user
    repo.delete_botim_user(&botim_user_id).await.unwrap();

    // get the botim user entities
    let botim_user_entities = repo
      .get_auids_by_phone_numbers(&[botim_user_entities[0].phone_number.clone()])
      .await
      .unwrap();
    assert!(botim_user_entities.is_empty());
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;

    let botim_user_entities = create_test_botim_user_entities();
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // First, save the entities
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .expect("Failed to save botim user entities");

    // Extract AUIDs from the entities
    let botim_user_ids: Vec<BotimUserId> = botim_user_entities
      .iter()
      .map(|entity| entity.botim_user_id.clone())
      .collect();

    // Test get_botim_user_entities_by_auids
    let result = repo.get_botim_user_entities_by_auids(&botim_user_ids).await;
    assert!(result.is_ok());

    let retrieved_entities = result.unwrap();
    assert!(!retrieved_entities.is_empty());

    // Verify the IDs match
    let retrieved_botim_user_ids: Vec<BotimUserId> = retrieved_entities
      .iter()
      .map(|entity| entity.botim_user_id.clone())
      .collect();

    for botim_user_id in &botim_user_ids {
      assert!(retrieved_botim_user_ids.contains(botim_user_id));
    }
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids_empty_input() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Test with empty input
    let empty_botim_user_ids: Vec<BotimUserId> = vec![];
    let result = repo.get_botim_user_entities_by_auids(&empty_botim_user_ids).await;

    assert!(result.is_ok(), "Should handle empty input gracefully");
    let retrieved_entities = result.unwrap();
    assert!(
      retrieved_entities.is_empty(),
      "Should return empty result for empty input"
    );
    println!("✅ Empty input test passed");
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids_nonexistent() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Test with non-existent AUIDs
    let non_existent_botim_user_ids = vec![
      BotimUserId::new(Auid::new(UuidBytes::generate())),
      BotimUserId::new(Auid::new(UuidBytes::generate())),
      BotimUserId::new(Auid::new(UuidBytes::generate())),
    ];

    let result = repo
      .get_botim_user_entities_by_auids(&non_existent_botim_user_ids)
      .await;
    assert!(result.is_ok(), "Should handle non-existent AUIDs gracefully");

    let retrieved_entities = result.unwrap();
    assert!(
      retrieved_entities.is_empty(),
      "Should return empty result for non-existent AUIDs"
    );
    println!("✅ Non-existent AUIDs test passed");
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids_partial_match() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Create and save test entities
    let saved_entities = create_test_botim_user_entities();
    repo
      .save_botim_user_entities_idempotently(&saved_entities)
      .await
      .expect("Failed to save botim user entities");

    // Create a mix of existing and non-existing AUIDs
    let mut mixed_botim_user_ids: Vec<BotimUserId> = saved_entities
      .iter()
      .map(|entity| entity.botim_user_id.clone())
      .collect();

    // Add some non-existent AUIDs
    mixed_botim_user_ids.push(BotimUserId::new(Auid::new(UuidBytes::generate())));
    mixed_botim_user_ids.push(BotimUserId::new(Auid::new(UuidBytes::generate())));

    let result = repo.get_botim_user_entities_by_auids(&mixed_botim_user_ids).await;
    assert!(result.is_ok(), "Should handle partial matches gracefully");

    let retrieved_entities = result.unwrap();
    assert_eq!(
      retrieved_entities.len(),
      saved_entities.len(),
      "Should return only existing entities"
    );

    // Verify that all retrieved entities are from the saved entities
    for retrieved_entity in &retrieved_entities {
      assert!(
        saved_entities
          .iter()
          .any(|saved| saved.botim_user_id == retrieved_entity.botim_user_id),
        "Retrieved entity should match saved entities"
      );
    }
    println!(
      "✅ Partial match test passed - found {}/{} entities",
      retrieved_entities.len(),
      mixed_botim_user_ids.len()
    );
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids_data_integrity() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Create and save test entities
    let saved_entities = create_test_botim_user_entities();
    repo
      .save_botim_user_entities_idempotently(&saved_entities)
      .await
      .expect("Failed to save botim user entities");

    let botim_user_ids: Vec<BotimUserId> = saved_entities
      .iter()
      .map(|entity| entity.botim_user_id.clone())
      .collect();

    let result = repo.get_botim_user_entities_by_auids(&botim_user_ids).await;
    assert!(result.is_ok(), "Should retrieve entities successfully");

    let retrieved_entities = result.unwrap();
    assert_eq!(
      retrieved_entities.len(),
      saved_entities.len(),
      "Should retrieve all saved entities"
    );

    // Verify data integrity field by field
    for saved_entity in &saved_entities {
      let retrieved_entity = retrieved_entities
        .iter()
        .find(|e| e.botim_user_id == saved_entity.botim_user_id)
        .expect("Should find corresponding retrieved entity");

      // Verify all fields match
      assert_eq!(
        retrieved_entity.botim_user_id, saved_entity.botim_user_id,
        "BotimUserId should match"
      );
      assert_eq!(
        retrieved_entity.auid_repr, saved_entity.auid_repr,
        "AuidRepr should match"
      );
      assert_eq!(
        retrieved_entity.phone_number, saved_entity.phone_number,
        "PhoneNumber should match"
      );
      // Note: We don't check created_at and updated_at for exact match as they might have slight differences
      // due to database precision, but we verify they exist
      assert!(!retrieved_entity.created_at.inner_ref().to_string().is_empty());
      assert!(!retrieved_entity.updated_at.inner_ref().to_string().is_empty());
    }
    println!("✅ Data integrity test passed - all fields match correctly");
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids_large_batch() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Create a larger batch of test entities (10 entities)
    let nanos = std::time::SystemTime::now()
      .duration_since(std::time::UNIX_EPOCH)
      .unwrap()
      .as_nanos();
    let suffix = (nanos % 100_000_000) as u64;

    let large_batch_entities: Vec<BotimUserEntity> = (0 .. 10)
      .map(|i| BotimUserEntity {
        botim_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
        auid_repr:     AuidRepr::new(format!("large_batch_user_{i}")),
        phone_number:  PhoneNumber::new(14000000000u64 + suffix + i as u64),
        created_at:    CreateAt::new(Utc::now()),
        updated_at:    UpdateAt::new(Utc::now()),
      })
      .collect();

    // Save the large batch
    repo
      .save_botim_user_entities_idempotently(&large_batch_entities)
      .await
      .expect("Failed to save large batch of botim user entities");

    let botim_user_ids: Vec<BotimUserId> = large_batch_entities
      .iter()
      .map(|entity| entity.botim_user_id.clone())
      .collect();

    let result = repo.get_botim_user_entities_by_auids(&botim_user_ids).await;
    assert!(result.is_ok(), "Should handle large batch query successfully");

    let retrieved_entities = result.unwrap();
    assert_eq!(
      retrieved_entities.len(),
      large_batch_entities.len(),
      "Should retrieve all entities from large batch"
    );

    // Verify all IDs are present
    let retrieved_ids: Vec<BotimUserId> = retrieved_entities.iter().map(|e| e.botim_user_id.clone()).collect();

    for original_id in &botim_user_ids {
      assert!(
        retrieved_ids.contains(original_id),
        "Should find all original IDs in retrieved results"
      );
    }
    println!(
      "✅ Large batch test passed - handled {} entities successfully",
      large_batch_entities.len()
    );
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids_sql_injection_safety() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Create test entity with normal data
    let normal_entity = BotimUserEntity {
      botim_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
      auid_repr:     AuidRepr::new("normal_user".to_string()),
      phone_number:  PhoneNumber::new(15000000000u64),
      created_at:    CreateAt::new(Utc::now()),
      updated_at:    UpdateAt::new(Utc::now()),
    };

    repo
      .save_botim_user_entities_idempotently(&[normal_entity.clone()])
      .await
      .expect("Failed to save normal entity");

    // Test that the query works correctly with valid UUID bytes
    let valid_botim_user_ids = vec![normal_entity.botim_user_id.clone()];
    let result = repo.get_botim_user_entities_by_auids(&valid_botim_user_ids).await;
    assert!(result.is_ok(), "Should handle valid UUID bytes correctly");

    let retrieved_entities = result.unwrap();
    assert_eq!(retrieved_entities.len(), 1, "Should retrieve the normal entity");
    assert_eq!(
      retrieved_entities[0].botim_user_id, normal_entity.botim_user_id,
      "Should retrieve the correct entity"
    );

    println!("✅ SQL injection safety test passed - parameterized queries work correctly");
  }

  #[tokio::test]
  async fn test_get_botim_user_entities_by_auids_duplicate_ids() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;
    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Create and save test entities
    let saved_entities = create_test_botim_user_entities();
    repo
      .save_botim_user_entities_idempotently(&saved_entities)
      .await
      .expect("Failed to save botim user entities");

    // Create a vector with duplicate IDs
    let mut duplicate_botim_user_ids = vec![saved_entities[0].botim_user_id.clone()];
    duplicate_botim_user_ids.push(saved_entities[0].botim_user_id.clone()); // Duplicate
    duplicate_botim_user_ids.push(saved_entities[1].botim_user_id.clone());
    duplicate_botim_user_ids.push(saved_entities[1].botim_user_id.clone()); // Duplicate

    let result = repo.get_botim_user_entities_by_auids(&duplicate_botim_user_ids).await;
    assert!(result.is_ok(), "Should handle duplicate IDs gracefully");

    let retrieved_entities = result.unwrap();
    // SQL should naturally deduplicate results
    assert_eq!(
      retrieved_entities.len(),
      saved_entities.len(),
      "Should return deduplicated results"
    );

    // Verify both unique entities are present
    let retrieved_ids: Vec<BotimUserId> = retrieved_entities.iter().map(|e| e.botim_user_id.clone()).collect();

    for saved_entity in &saved_entities {
      assert!(
        retrieved_ids.contains(&saved_entity.botim_user_id),
        "Should find all unique saved entities"
      );
    }
    println!("✅ Duplicate IDs test passed - SQL naturally deduplicates results");
  }

  #[tokio::test]
  async fn test_cache_hit_rate() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;

    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Reset cache statistics
    repo.reset_cache_stats();

    // Prepare test data
    let botim_user_entities = create_test_botim_user_entities();
    let phone_numbers: Vec<PhoneNumber> = botim_user_entities
      .iter()
      .map(|entity| entity.phone_number.clone())
      .collect();

    // Save data first
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .expect("Failed to save botim user entities");

    // Reset cache statistics after save to test query behavior
    repo.reset_cache_stats();

    // First query - cache hit (because save automatically cached the data)
    let _result1 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result1.is_ok());

    // Second query - cache hit
    let _result2 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result2.is_ok());

    // Third query - cache hit
    let _result3 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result3.is_ok());

    // Get cache statistics
    let stats = repo.get_cache_stats();

    println!("Cache statistics:");
    println!("  Hit count: {}", stats.hit_count);
    println!("  Miss count: {}", stats.miss_count);
    println!("  Total count: {}", stats.total_count);
    println!("  Hit rate: {:.2}%", stats.hit_rate * 100.0);

    // Verify statistics - All queries should hit cache since save automatically cached the data
    assert_eq!(stats.hit_count, 3); // All queries should hit cache
    assert_eq!(stats.miss_count, 0); // No cache misses
    assert_eq!(stats.total_count, 3); // Total 3 queries
    assert!((stats.hit_rate - 1.0).abs() < 0.001); // Hit rate should be 100%
  }

  #[tokio::test]
  async fn test_cache_update_after_save() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;

    let repo = BotimUserRepoImpl::new(pool, redis_pool);
    repo.reset_cache_stats();

    // Create initial test data
    let mut botim_user_entities = create_test_botim_user_entities();
    let phone_numbers: Vec<PhoneNumber> = botim_user_entities
      .iter()
      .map(|entity| entity.phone_number.clone())
      .collect();

    // Save initial data
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .expect("Failed to save initial entities");

    // First query - should be cache miss because we just saved
    let _result1 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result1.is_ok());

    // Second query - should be cache hit
    let _result2 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result2.is_ok());

    // Update the entities (same phone numbers, different auid_repr)
    botim_user_entities[0].auid_repr = AuidRepr::new("updated_auid_1".to_string());
    botim_user_entities[1].auid_repr = AuidRepr::new("updated_auid_2".to_string());

    // Save updated data - this should update the cache
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .expect("Failed to save updated entities");

    // Query again - should get updated data from cache
    let updated_result = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(updated_result.is_ok());

    let updated_entities = updated_result.unwrap();
    assert_eq!(updated_entities.len(), 2);

    // Verify the entities were updated
    let updated_auid_reprs: Vec<String> = updated_entities
      .iter()
      .map(|entity| entity.auid_repr.inner_ref().to_string())
      .collect();

    assert!(updated_auid_reprs.contains(&"updated_auid_1".to_string()));
    assert!(updated_auid_reprs.contains(&"updated_auid_2".to_string()));

    println!("Cache update after save test completed successfully");
  }

  #[tokio::test]
  async fn test_cache_miss_scenario() {
    let pool = create_test_pool().await;
    let redis_pool = create_test_redis_pool().await;

    let repo = BotimUserRepoImpl::new(pool, redis_pool);

    // Reset cache statistics
    repo.reset_cache_stats();

    // Prepare test data
    let botim_user_entities = create_test_botim_user_entities();
    let phone_numbers: Vec<PhoneNumber> = botim_user_entities
      .iter()
      .map(|entity| entity.phone_number.clone())
      .collect();

    // Save data first
    repo
      .save_botim_user_entities_idempotently(&botim_user_entities)
      .await
      .expect("Failed to save botim user entities");

    // Clear cache to simulate cache miss scenario
    repo.cache.delete_by_phone_numbers(&phone_numbers).await.ok();

    // Reset statistics after clearing cache
    repo.reset_cache_stats();

    // First query - should be cache miss
    let _result1 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result1.is_ok());

    // Second query - should be cache hit
    let _result2 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result2.is_ok());

    // Third query - should be cache hit
    let _result3 = repo.get_auids_by_phone_numbers(&phone_numbers).await;
    assert!(_result3.is_ok());

    // Get cache statistics
    let stats = repo.get_cache_stats();

    println!("Cache miss scenario statistics:");
    println!("  Hit count: {}", stats.hit_count);
    println!("  Miss count: {}", stats.miss_count);
    println!("  Total count: {}", stats.total_count);
    println!("  Hit rate: {:.2}%", stats.hit_rate * 100.0);

    // Verify statistics - First query miss, subsequent hits
    assert_eq!(stats.hit_count, 2); // Last two queries should hit cache
    assert_eq!(stats.miss_count, 1); // First query should miss cache
    assert_eq!(stats.total_count, 3); // Total 3 queries
    assert!((stats.hit_rate - 0.6667).abs() < 0.001); // Hit rate should be about 66.67%
  }
}
