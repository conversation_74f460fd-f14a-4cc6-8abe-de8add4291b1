use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::{id::uuid::UuidBytes,
                          value::time::{CreateAt,
                                        UpdateAt}},
     chrono::DateTime,
     fred::prelude::*,
     hex,
     serde::{Deserialize,
             Serialize},
     serde_json,
     std::{collections::hash_map::DefaultHasher,
           hash::{Hash,
                  Hasher},
           sync::atomic::{AtomicU64,
                          Ordering}},
     tracing::{debug,
               error}};

// Cache data structure for BotimUser
#[derive(Debug, Clone, Serialize, Deserialize)]
pub(super) struct CachedBotimUser {
  pub botim_user_id: String,
  pub auid_repr:     String,
  pub phone_number:  u64,
  pub created_at:    i64,
  pub updated_at:    i64,
}

impl From<&BotimUserEntity> for CachedBotimUser {
  fn from(entity: &BotimUserEntity) -> Self {
    Self {
      botim_user_id: hex::encode(entity.botim_user_id.inner_ref().inner_ref().inner_ref()),
      auid_repr:     entity.auid_repr.inner_ref().to_string(),
      phone_number:  *entity.phone_number.inner_ref(),
      created_at:    entity.created_at.inner_ref().timestamp_millis(),
      updated_at:    entity.updated_at.inner_ref().timestamp_millis(),
    }
  }
}

impl TryFrom<CachedBotimUser> for BotimUserEntity {
  type Error = FriendshipError;

  fn try_from(cached: CachedBotimUser) -> Result<Self, Self::Error> {
    // Decode hex string to UUID bytes
    let uuid_bytes = hex::decode(&cached.botim_user_id)
      .map_err(|e| FriendshipError::InvalidUserId(format!("Invalid hex UUID: {e}")))?;

    if uuid_bytes.len() != 16 {
      return Err(FriendshipError::InvalidUserId("UUID must be 16 bytes".to_string()));
    }

    let mut uuid_array = [0u8; 16];
    uuid_array.copy_from_slice(&uuid_bytes);

    let created_at = DateTime::from_timestamp_millis(cached.created_at)
      .ok_or_else(|| FriendshipError::InvalidInput("Invalid created_at timestamp".to_string()))?;
    let updated_at = DateTime::from_timestamp_millis(cached.updated_at)
      .ok_or_else(|| FriendshipError::InvalidInput("Invalid updated_at timestamp".to_string()))?;

    Ok(BotimUserEntity {
      botim_user_id: BotimUserId::new(Auid::new(UuidBytes::new(uuid_array))),
      auid_repr:     AuidRepr::new(cached.auid_repr),
      phone_number:  PhoneNumber::new(cached.phone_number),
      created_at:    CreateAt::new(created_at),
      updated_at:    UpdateAt::new(updated_at),
    })
  }
}

/// Cache statistics information
#[derive(Debug, Clone)]
pub struct CacheStats {
  pub hit_count:   u64,
  pub miss_count:  u64,
  pub total_count: u64,
  pub hit_rate:    f64,
}

/// BotimUser cache manager
pub(super) struct BotimUserCache {
  redis_pool:  fred::clients::Pool,
  hit_count:   AtomicU64,
  miss_count:  AtomicU64,
  ttl_seconds: u64,
}

impl BotimUserCache {
  pub fn new(redis_pool: fred::clients::Pool) -> Self {
    Self {
      redis_pool,
      hit_count: AtomicU64::new(0),
      miss_count: AtomicU64::new(0),
      ttl_seconds: 1800, // Default 30 minutes
    }
  }

  #[allow(dead_code)]
  pub(super) fn new_with_ttl(redis_pool: fred::clients::Pool, ttl_seconds: u64) -> Self {
    Self {
      redis_pool,
      hit_count: AtomicU64::new(0),
      miss_count: AtomicU64::new(0),
      ttl_seconds,
    }
  }

  // Generate cache key based on phone numbers
  fn generate_cache_key(&self, phone_numbers: &[PhoneNumber]) -> String {
    let mut hasher = DefaultHasher::new();
    let mut sorted_phones: Vec<String> = phone_numbers.iter().map(|p| p.inner_ref().to_string()).collect();
    sorted_phones.sort();
    sorted_phones.hash(&mut hasher);

    format!("botim_users:phones:{:x}", hasher.finish())
  }

  // Get data from cache
  #[tracing::instrument(name = "get_by_phone_numbers", skip(self, phone_numbers))]
  pub async fn get_by_phone_numbers(
    &self,
    phone_numbers: &[PhoneNumber],
  ) -> FriendshipResult<Option<Vec<BotimUserEntity>>> {
    let cache_key = self.generate_cache_key(phone_numbers);

    let cached_json: Option<String> = self
      .redis_pool
      .get::<Option<String>, _>(&cache_key)
      .await
      .map_err(|e| {
        error!("Failed to get from cache: {:?}", e);
        FriendshipError::RedisError(e.to_string())
      })?;

    match cached_json {
      Some(json) => {
        // Cache hit
        self.hit_count.fetch_add(1, Ordering::Relaxed);

        let cached_entities: Vec<CachedBotimUser> = serde_json::from_str(&json).map_err(|e| {
          error!("Failed to deserialize cached data: {:?}", e);
          FriendshipError::Deserialization(e.to_string())
        })?;

        // Convert to BotimUserEntity
        let entities: Result<Vec<BotimUserEntity>, FriendshipError> =
          cached_entities.into_iter().map(|cached| cached.try_into()).collect();

        debug!("Cache hit for phone numbers cache key: {}", cache_key);
        Ok(Some(entities?))
      }
      None => {
        // Cache miss
        self.miss_count.fetch_add(1, Ordering::Relaxed);

        debug!("Cache miss for phone numbers cache key: {}", cache_key);
        Ok(None)
      }
    }
  }

  // Set cache data
  #[tracing::instrument(name = "set_by_phone_numbers", skip(self, phone_numbers, entities))]
  pub async fn set_by_phone_numbers(
    &self,
    phone_numbers: &[PhoneNumber],
    entities: &[BotimUserEntity],
  ) -> FriendshipResult<()> {
    let cache_key = self.generate_cache_key(phone_numbers);

    // Convert to cache format
    let cached_entities: Vec<CachedBotimUser> = entities.iter().map(|entity| entity.into()).collect();

    let json = serde_json::to_string(&cached_entities).map_err(|e| {
      error!("Failed to serialize entities for cache: {:?}", e);
      FriendshipError::Deserialization(e.to_string())
    })?;

    // Set cache with configurable expiration
    let ttl = Expiration::EX(self.ttl_seconds as i64);
    self
      .redis_pool
      .set::<(), _, _>(&cache_key, json, Some(ttl), None, false)
      .await
      .map_err(|e| {
        error!("Failed to set to cache: {:?}", e);
        FriendshipError::RedisError(e.to_string())
      })?;

    debug!("Cached result for key: {}", cache_key);
    Ok(())
  }

  // Delete cache based on phone numbers
  #[tracing::instrument(name = "delete_by_phone_numbers", skip(self, phone_numbers))]
  #[allow(dead_code)]
  pub async fn delete_by_phone_numbers(&self, phone_numbers: &[PhoneNumber]) -> FriendshipResult<()> {
    let cache_key = self.generate_cache_key(phone_numbers);

    let _: i64 = self.redis_pool.del(&cache_key).await.map_err(|e| {
      error!("Failed to delete cache: {:?}", e);
      FriendshipError::RedisError(e.to_string())
    })?;

    debug!("Deleted cache for key: {}", cache_key);
    Ok(())
  }

  /// Get cache statistics
  pub fn get_cache_stats(&self) -> CacheStats {
    let hit_count = self.hit_count.load(Ordering::Relaxed);
    let miss_count = self.miss_count.load(Ordering::Relaxed);
    let total_count = hit_count + miss_count;

    let hit_rate = if total_count > 0 {
      hit_count as f64 / total_count as f64
    } else {
      0.0
    };

    CacheStats {
      hit_count,
      miss_count,
      total_count,
      hit_rate,
    }
  }

  /// Reset cache statistics
  pub fn reset_cache_stats(&self) {
    self.hit_count.store(0, Ordering::Relaxed);
    self.miss_count.store(0, Ordering::Relaxed);
  }
}
