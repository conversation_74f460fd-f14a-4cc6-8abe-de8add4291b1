//! Unified caching module
//!
//! This module provides a unified caching interface and implementations
//! for different types of data with optimized strategies.

pub mod cache_config;
pub mod cache_manager;
pub mod cache_strategy;

use {crate::all::FriendshipError,
     fred::prelude::*,
     serde::{Deserialize,
             Serialize},
     std::{sync::atomic::{AtomicU64,
                          Ordering},
           time::{Duration,
                  SystemTime,
                  UNIX_EPOCH}}};

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
  pub hit_count:   u64,
  pub miss_count:  u64,
  pub error_count: u64,
  pub total_count: u64,
  pub hit_rate:    f64,
}

impl CacheStats {
  pub fn new() -> Self {
    Self {
      hit_count:   0,
      miss_count:  0,
      error_count: 0,
      total_count: 0,
      hit_rate:    0.0,
    }
  }

  pub fn calculate_hit_rate(&mut self) {
    self.total_count = self.hit_count + self.miss_count;
    self.hit_rate = if self.total_count > 0 {
      self.hit_count as f64 / self.total_count as f64
    } else {
      0.0
    };
  }
}

/// Cache key builder for consistent key generation
pub struct CacheKeyBuilder;

impl CacheKeyBuilder {
  /// Build a cache key with namespace and version
  pub fn build_key(namespace: &str, key: &str, version: Option<&str>) -> String {
    match version {
      Some(v) => format!("{}:{}:{}", namespace, v, key),
      None => format!("{}:{}", namespace, key),
    }
  }

  /// Build a cache key for phone numbers
  pub fn build_phone_key(phone_numbers: &[u64]) -> String {
    let mut sorted_phones = phone_numbers.to_vec();
    sorted_phones.sort_unstable();
    let phones_str = sorted_phones
      .iter()
      .map(|p| p.to_string())
      .collect::<Vec<_>>()
      .join(",");
    format!("phones:{}", phones_str)
  }

  /// Build a cache key for user blocking status
  pub fn build_block_key(user_id: &str, blocked_user_id: &str) -> String {
    format!("block:{}:{}", user_id, blocked_user_id)
  }

  /// Build a cache key for contact data
  pub fn build_contact_key(owner_id: &str, phone_number: u64) -> String {
    format!("contact:{}:{}", owner_id, phone_number)
  }
}

/// Base cache trait for all cache implementations
#[async_trait::async_trait]
pub trait Cache<T>: Send + Sync {
  /// Get value from cache
  async fn get(&self, key: &str) -> Result<Option<T>, FriendshipError>;
  
  /// Set value in cache with TTL
  async fn set(&self, key: &str, value: &T, ttl: Option<Duration>) -> Result<(), FriendshipError>;
  
  /// Delete value from cache
  async fn delete(&self, key: &str) -> Result<(), FriendshipError>;
  
  /// Check if key exists in cache
  async fn exists(&self, key: &str) -> Result<bool, FriendshipError>;
  
  /// Get cache statistics
  fn get_stats(&self) -> CacheStats;
  
  /// Reset cache statistics
  fn reset_stats(&self);
}

/// Redis-based cache implementation with optimized serialization
pub struct RedisCache<T> {
  pool:        Pool,
  namespace:   String,
  default_ttl: Duration,
  hit_count:   AtomicU64,
  miss_count:  AtomicU64,
  error_count: AtomicU64,
  _phantom:    std::marker::PhantomData<T>,
}

impl<T> RedisCache<T>
where
  T: Serialize + for<'de> Deserialize<'de> + Send + Sync,
{
  pub fn new(pool: Pool, namespace: String, default_ttl: Duration) -> Self {
    Self {
      pool,
      namespace,
      default_ttl,
      hit_count: AtomicU64::new(0),
      miss_count: AtomicU64::new(0),
      error_count: AtomicU64::new(0),
      _phantom: std::marker::PhantomData,
    }
  }

  fn build_namespaced_key(&self, key: &str) -> String {
    CacheKeyBuilder::build_key(&self.namespace, key, None)
  }

  /// Get current timestamp for cache metadata
  fn current_timestamp() -> u64 {
    SystemTime::now()
      .duration_since(UNIX_EPOCH)
      .unwrap_or_default()
      .as_secs()
  }
}

#[async_trait::async_trait]
impl<T> Cache<T> for RedisCache<T>
where
  T: Serialize + for<'de> Deserialize<'de> + Send + Sync,
{
  async fn get(&self, key: &str) -> Result<Option<T>, FriendshipError> {
    let namespaced_key = self.build_namespaced_key(key);
    
    match self.pool.get::<Option<String>, _>(&namespaced_key).await {
      Ok(Some(json)) => {
        match serde_json::from_str::<T>(&json) {
          Ok(value) => {
            self.hit_count.fetch_add(1, Ordering::Relaxed);
            Ok(Some(value))
          }
          Err(e) => {
            self.error_count.fetch_add(1, Ordering::Relaxed);
            tracing::error!("Failed to deserialize cached value for key {}: {:?}", key, e);
            // Delete corrupted cache entry
            let _ = self.delete(key).await;
            Ok(None)
          }
        }
      }
      Ok(None) => {
        self.miss_count.fetch_add(1, Ordering::Relaxed);
        Ok(None)
      }
      Err(e) => {
        self.error_count.fetch_add(1, Ordering::Relaxed);
        tracing::error!("Redis error getting key {}: {:?}", key, e);
        Err(FriendshipError::RedisError(e.to_string()))
      }
    }
  }

  async fn set(&self, key: &str, value: &T, ttl: Option<Duration>) -> Result<(), FriendshipError> {
    let namespaced_key = self.build_namespaced_key(key);
    let ttl = ttl.unwrap_or(self.default_ttl);
    
    let json = serde_json::to_string(value).map_err(|e| {
      self.error_count.fetch_add(1, Ordering::Relaxed);
      FriendshipError::RedisError(format!("Serialization error: {}", e))
    })?;

    let expiration = Expiration::EX(ttl.as_secs() as i64);
    
    self
      .pool
      .set::<(), _, _>(&namespaced_key, json, Some(expiration), None, false)
      .await
      .map_err(|e| {
        self.error_count.fetch_add(1, Ordering::Relaxed);
        tracing::error!("Redis error setting key {}: {:?}", key, e);
        FriendshipError::RedisError(e.to_string())
      })?;

    Ok(())
  }

  async fn delete(&self, key: &str) -> Result<(), FriendshipError> {
    let namespaced_key = self.build_namespaced_key(key);
    
    self.pool.del::<i64, _>(&namespaced_key).await.map_err(|e| {
      self.error_count.fetch_add(1, Ordering::Relaxed);
      tracing::error!("Redis error deleting key {}: {:?}", key, e);
      FriendshipError::RedisError(e.to_string())
    })?;

    Ok(())
  }

  async fn exists(&self, key: &str) -> Result<bool, FriendshipError> {
    let namespaced_key = self.build_namespaced_key(key);
    
    let exists: i64 = self.pool.exists(&namespaced_key).await.map_err(|e| {
      self.error_count.fetch_add(1, Ordering::Relaxed);
      tracing::error!("Redis error checking existence of key {}: {:?}", key, e);
      FriendshipError::RedisError(e.to_string())
    })?;

    Ok(exists > 0)
  }

  fn get_stats(&self) -> CacheStats {
    let hit_count = self.hit_count.load(Ordering::Relaxed);
    let miss_count = self.miss_count.load(Ordering::Relaxed);
    let error_count = self.error_count.load(Ordering::Relaxed);
    let total_count = hit_count + miss_count;

    let hit_rate = if total_count > 0 {
      hit_count as f64 / total_count as f64
    } else {
      0.0
    };

    CacheStats {
      hit_count,
      miss_count,
      error_count,
      total_count,
      hit_rate,
    }
  }

  fn reset_stats(&self) {
    self.hit_count.store(0, Ordering::Relaxed);
    self.miss_count.store(0, Ordering::Relaxed);
    self.error_count.store(0, Ordering::Relaxed);
  }
}

/// Cache warming utilities
pub struct CacheWarmer;

impl CacheWarmer {
  /// Warm cache with frequently accessed data
  pub async fn warm_cache<T, F, Fut>(
    cache: &dyn Cache<T>,
    keys: Vec<String>,
    data_loader: F,
  ) -> Result<usize, FriendshipError>
  where
    T: Send + Sync,
    F: Fn(String) -> Fut + Send + Sync,
    Fut: std::future::Future<Output = Result<Option<T>, FriendshipError>> + Send,
  {
    let mut warmed_count = 0;
    
    for key in keys {
      // Check if key already exists in cache
      if cache.exists(&key).await.unwrap_or(false) {
        continue;
      }
      
      // Load data and cache it
      if let Ok(Some(data)) = data_loader(key.clone()).await {
        if cache.set(&key, &data, None).await.is_ok() {
          warmed_count += 1;
        }
      }
    }
    
    Ok(warmed_count)
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_cache_key_builder() {
    let key = CacheKeyBuilder::build_key("test", "key1", Some("v1"));
    assert_eq!(key, "test:v1:key1");

    let key = CacheKeyBuilder::build_key("test", "key1", None);
    assert_eq!(key, "test:key1");
  }

  #[test]
  fn test_phone_key_builder() {
    let phones = vec![123456789, 987654321, 555555555];
    let key = CacheKeyBuilder::build_phone_key(&phones);
    // Should be sorted
    assert_eq!(key, "phones:123456789,555555555,987654321");
  }

  #[test]
  fn test_block_key_builder() {
    let key = CacheKeyBuilder::build_block_key("user1", "user2");
    assert_eq!(key, "block:user1:user2");
  }

  #[test]
  fn test_contact_key_builder() {
    let key = CacheKeyBuilder::build_contact_key("owner1", 123456789);
    assert_eq!(key, "contact:owner1:123456789");
  }

  #[test]
  fn test_cache_stats() {
    let mut stats = CacheStats::new();
    stats.hit_count = 80;
    stats.miss_count = 20;
    stats.calculate_hit_rate();
    
    assert_eq!(stats.total_count, 100);
    assert_eq!(stats.hit_rate, 0.8);
  }
}
