use {crate::repo::po::contacts::ContactPo,
     async_trait::async_trait,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::{ContactEntity,
                                       ContactOwnerId,
                                       ContactRepo,
                                       FriendshipError,
                                       FriendshipResult,
                                       PhoneNumber},
     sqlx::{self,
            MySql,
            Pool,
            Row},
     tracing::{debug,
               error}};

pub struct ContactRepoImpl {
  pool: Pool<MySql>,
}

impl ContactRepoImpl {
  pub fn new(pool: Pool<MySql>) -> Self {
    Self { pool }
  }
}

#[async_trait]
impl ContactRepo for ContactRepoImpl {
  #[tracing::instrument(name = "update_contacts_for_owner_idempotentially", skip(self, owner, contacts))]
  async fn update_contacts_for_owner_idempotentially(
    &self,
    owner: &ContactOwnerId,
    contacts: &[ContactEntity],
  ) -> FriendshipResult<()> {
    if contacts.is_empty() {
      debug!("Contacts vector is empty, skipping update");
      return Ok(());
    }

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    // Build the SQL query with placeholders
    let placeholders = (0 .. contacts.len())
      .map(|_| "(?, ?, ?, ?, ?, ?)")
      .collect::<Vec<_>>()
      .join(", ");
    let query = format!(
      "INSERT INTO t_contacts (contact_id, owner_id, phone_number, contact_name, status, updated_at) VALUES \
       {placeholders} ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at), contact_name = VALUES(contact_name), \
       status = VALUES(status), phone_number = VALUES(phone_number)"
    );

    // Bind parameters to the query
    let query_builder = contacts.iter().fold(sqlx::query(&query), |builder, contact| {
      builder
        .bind(contact.contact_id.inner_ref().inner_ref().to_vec())
        .bind(owner.inner_ref().inner_ref().inner_ref().to_vec())
        .bind(*contact.phone_number.inner_ref())
        .bind(contact.contact_name.as_ref().map(|n| n.inner_ref().to_string()))
        .bind(contact.status.code())
        .bind(contact.updated_at.inner_ref())
    });

    debug!("Executing query: {}", query);

    let query_result = query_builder.execute(&mut *conn).await.map_err(|e| {
      debug!("Error executing query: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    if query_result.rows_affected() == 0 {
      error!("Failed to update contacts for owner: {:?}", owner);
      return Err(FriendshipError::DatabaseError(sqlx::Error::RowNotFound.to_string()));
    }

    Ok(())
  }

  #[tracing::instrument(name = "send_save_uid_notification", skip(self, owner_id, phone_number_filters))]
  async fn send_save_uid_notification(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<()> {
    debug!(
      "send save uid notification to {} phone numbers for owner: {:?}",
      phone_number_filters.len(),
      owner_id
    );
    Ok(())
  }

  #[tracing::instrument(name = "get_contact_owners_with_friend_phone_number", skip(self, phone_number))]
  async fn get_contact_owners_with_friend_phone_number(
    &self,
    phone_number: &PhoneNumber,
  ) -> FriendshipResult<Vec<ContactOwnerId>> {
    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let query = "SELECT contact_id, owner_id, phone_number, contact_name, status, created_at, updated_at FROM \
                 t_contacts WHERE phone_number = ? and status = 1";

    let result = sqlx::query_as::<_, ContactPo>(query)
      .bind(*phone_number.inner_ref())
      .fetch_all(&mut *conn)
      .await
      .map_err(|e| {
        error!("Failed to fetch contact owners: {:?}", e);
        FriendshipError::DatabaseError(e.to_string())
      })?;

    Ok(
      result
        .into_iter()
        .filter_map(|po| match po.to_contact_entity() {
          Ok(entity) => Some(entity.owner_id),
          Err(e) => {
            error!("Failed to convert contact entity: {:?}", e);
            None
          }
        })
        .collect(),
    )
  }

  #[tracing::instrument(
    name = "get_existing_contact_phone_numbers",
    skip(self, owner_id, phone_number_filters)
  )]
  async fn get_existing_contact_phone_numbers(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<PhoneNumber>> {
    if phone_number_filters.is_empty() {
      debug!("No phone number filters provided, returning empty result");
      return Ok(Vec::new());
    }

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let placeholders = phone_number_filters.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
    let query = format!(
      "SELECT phone_number FROM t_contacts WHERE owner_id = ? AND phone_number IN ({placeholders}) AND status = 1"
    );

    let query_builder = phone_number_filters.iter().fold(
      sqlx::query(&query).bind(owner_id.inner_ref().inner_ref().inner_ref().to_vec()),
      |builder, phone_number| builder.bind(*phone_number.inner_ref()),
    );

    let result = query_builder.fetch_all(&mut *conn).await.map_err(|e| {
      error!("Failed to get existing contact phone numbers: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let existing_phone_numbers: Vec<PhoneNumber> = result
      .into_iter()
      .filter_map(|row| match row.try_get::<u64, _>("phone_number") {
        Ok(phone_number) => Some(PhoneNumber::new(phone_number)),
        Err(e) => {
          error!("Failed to extract phone number from row: {:?}", e);
          None
        }
      })
      .collect();

    debug!(
      "Found {} existing contact phone numbers for owner {:?} out of {} filters",
      existing_phone_numbers.len(),
      owner_id,
      phone_number_filters.len()
    );

    Ok(existing_phone_numbers)
  }

  async fn get_contact_owners_with_friend_phone_number_and_owner_ids(
    &self,
    phone_number: &PhoneNumber,
    owner_ids: &[ContactOwnerId],
  ) -> FriendshipResult<Vec<ContactOwnerId>> {
    if owner_ids.is_empty() {
      return Ok(vec![]);
    }

    let mut conn = self.pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let placeholders = owner_ids.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
    let query = format!(
      "SELECT contact_id, owner_id, phone_number, contact_name, status, created_at, updated_at FROM t_contacts WHERE \
       phone_number = ? AND owner_id IN ({placeholders}) AND status = 1"
    );

    let query_builder = owner_ids.iter().fold(
      sqlx::query_as::<_, ContactPo>(&query).bind(*phone_number.inner_ref()),
      |builder, owner_id| builder.bind(owner_id.inner_ref().inner_ref().inner_ref().to_vec()),
    );

    let result = query_builder.fetch_all(&mut *conn).await.map_err(|e| {
      error!("Failed to fetch contact owners: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    Ok(
      result
        .into_iter()
        .filter_map(|po| match po.to_contact_entity() {
          Ok(entity) => Some(entity.owner_id),
          Err(e) => {
            error!("Failed to convert contact entity: {:?}", e);
            None
          }
        })
        .collect(),
    )
  }
}

#[cfg(test)]
mod tests {
  use {at_deps::at_friendship::*,
       at_friendship_domain_model::all::*,
       at_internal_deps::at_friendship::*,
       at_lib_comm_domain::{id::uuid::UuidBytes,
                            value::time::{CreateAt,
                                          UpdateAt}},
       chrono::Utc,
       sqlx::mysql::MySqlPoolOptions};

  use super::*;

  // Create shared test database connection function
  async fn create_test_pool() -> Pool<MySql> {
    let db_url = "mysql://friendshipuser:friendship_pwpPKDushT1h3J@***********:4000/friendship";

    // Create and return connection pool
    MySqlPoolOptions::new()
      .max_connections(5)
      .connect(db_url)
      .await
      .expect("Failed to create test database pool")
  }

  // Create test contact entities
  fn create_test_contact_entities(owner: &ContactOwnerId) -> Vec<ContactEntity> {
    vec![
      ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner.clone(),
        phone_number: PhoneNumber::new(13800138000u64),
        contact_name: Some(ContactName::new("Test User".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::new(Utc::now()),
        updated_at:   UpdateAt::new(Utc::now()),
      },
      ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner.clone(),
        phone_number: PhoneNumber::new(13800138001u64),
        contact_name: Some(ContactName::new("Test User 2".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::new(Utc::now()),
        updated_at:   UpdateAt::new(Utc::now()),
      },
    ]
  }

  fn create_test_contact_entity_with_empty_vector() -> Vec<ContactEntity> {
    vec![]
  }

  #[tokio::test]
  #[ignore] // Skip this test since t_contacts table doesn't exist
  async fn test_update_contacts() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;

    // Prepare test data
    let user_id = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let contacts = create_test_contact_entities(&user_id);

    let repo = ContactRepoImpl::new(pool);

    // Test first call
    let result = repo
      .update_contacts_for_owner_idempotentially(&user_id, &contacts)
      .await;
    assert!(result.is_ok(), "First update contacts call failed: {result:?}");

    result.unwrap();
    assert_eq!(contacts.len(), 2, "Expected 2 contacts, got {}", contacts.len());
  }

  #[tokio::test]
  #[ignore] // Skip this test since t_contacts table doesn't exist
  async fn test_update_contacts_with_empty_vector() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;

    // Prepare test data
    let user_id = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let contacts = create_test_contact_entity_with_empty_vector();

    let repo = ContactRepoImpl::new(pool);

    // Test first call
    let result = repo
      .update_contacts_for_owner_idempotentially(&user_id, &contacts)
      .await;
    assert!(result.is_ok(), "First update contacts call failed: {result:?}");

    result.unwrap();
    assert_eq!(contacts.len(), 0, "Expected 0 contacts, got {}", contacts.len());
  }

  #[tokio::test]
  #[ignore] // Skip this test since t_contacts table doesn't exist
  async fn test_get_contact_owners_with_friend_phone_number() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;

    // Prepare test data - create multiple owners with the same phone number
    let owner1 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let owner2 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let shared_phone = PhoneNumber::new(13900139999u64);

    let contacts1 = vec![ContactEntity {
      contact_id:   ContactId::new(UuidBytes::generate()),
      owner_id:     owner1.clone(),
      phone_number: shared_phone.clone(),
      contact_name: Some(ContactName::new("Shared Contact 1".to_string())),
      status:       ContactStatus::Normal,
      created_at:   CreateAt::new(Utc::now()),
      updated_at:   UpdateAt::new(Utc::now()),
    }];

    let contacts2 = vec![ContactEntity {
      contact_id:   ContactId::new(UuidBytes::generate()),
      owner_id:     owner2.clone(),
      phone_number: shared_phone.clone(),
      contact_name: Some(ContactName::new("Shared Contact 2".to_string())),
      status:       ContactStatus::Normal,
      created_at:   CreateAt::new(Utc::now()),
      updated_at:   UpdateAt::new(Utc::now()),
    }];

    let repo = ContactRepoImpl::new(pool);

    // First, add contacts for both owners
    let _result1 = repo
      .update_contacts_for_owner_idempotentially(&owner1, &contacts1)
      .await;
    let _result2 = repo
      .update_contacts_for_owner_idempotentially(&owner2, &contacts2)
      .await;

    // Test getting contact owners with shared phone number
    let result = repo.get_contact_owners_with_friend_phone_number(&shared_phone).await;

    assert!(
      result.is_ok(),
      "get_contact_owners_with_friend_phone_number failed: {result:?}"
    );
    let owners = result.unwrap();

    // Should find both owners who have contacts with this phone number
    assert!(owners.len() >= 2, "Expected at least 2 owners, got {}", owners.len());
    assert!(owners.contains(&owner1), "Should contain owner1");
    assert!(owners.contains(&owner2), "Should contain owner2");
  }

  #[tokio::test]
  #[ignore] // Skip this test since t_contacts table doesn't exist
  async fn test_get_existing_contact_phone_numbers() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;

    // Prepare test data
    let owner = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let contacts = create_test_contact_entities(&owner);

    let repo = ContactRepoImpl::new(pool);

    // First, add contacts
    let _result = repo.update_contacts_for_owner_idempotentially(&owner, &contacts).await;

    // Test getting existing phone numbers
    let phone_filters = vec![
      PhoneNumber::new(13800138000u64), // This should exist
      PhoneNumber::new(13800138001u64), // This should exist
      PhoneNumber::new(13800138999u64), // This should not exist
    ];

    let result = repo.get_existing_contact_phone_numbers(&owner, &phone_filters).await;

    assert!(result.is_ok(), "get_existing_contact_phone_numbers failed: {result:?}");
    let existing_phones = result.unwrap();

    // Should find the two existing phone numbers
    assert_eq!(
      existing_phones.len(),
      2,
      "Expected 2 existing phone numbers, got {}",
      existing_phones.len()
    );
    assert!(
      existing_phones.contains(&PhoneNumber::new(13800138000u64)),
      "Should contain first phone number"
    );
    assert!(
      existing_phones.contains(&PhoneNumber::new(13800138001u64)),
      "Should contain second phone number"
    );
    assert!(
      !existing_phones.contains(&PhoneNumber::new(13800138999u64)),
      "Should not contain non-existent phone number"
    );
  }

  #[tokio::test]
  #[ignore] // Skip this test since t_contacts table doesn't exist
  async fn test_get_contact_owners_with_friend_phone_number_and_owner_ids() {
    // Use shared function to create connection pool
    let pool = create_test_pool().await;

    // Prepare test data - create multiple owners with the same phone number
    let owner1 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let owner2 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let owner3 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let shared_phone = PhoneNumber::new(13900139888u64);

    // Create contacts for all three owners with the same phone number
    let contacts = [&owner1, &owner2, &owner3]
      .iter()
      .map(|owner| ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     (*owner).clone(),
        phone_number: shared_phone.clone(),
        contact_name: Some(ContactName::new("Shared Contact".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::new(Utc::now()),
        updated_at:   UpdateAt::new(Utc::now()),
      })
      .collect::<Vec<_>>();

    let repo = ContactRepoImpl::new(pool);

    // Add contacts for all owners
    for (i, contact) in contacts.iter().enumerate() {
      let owner = [&owner1, &owner2, &owner3][i];
      let _result = repo
        .update_contacts_for_owner_idempotentially(owner, &[contact.clone()])
        .await;
    }

    // Test filtering by specific owner IDs
    let filter_owners = vec![owner1.clone(), owner3.clone()]; // Only include owner1 and owner3

    let result = repo
      .get_contact_owners_with_friend_phone_number_and_owner_ids(&shared_phone, &filter_owners)
      .await;

    assert!(
      result.is_ok(),
      "get_contact_owners_with_friend_phone_number_and_owner_ids failed: {result:?}"
    );
    let filtered_owners = result.unwrap();

    // Should only find owner1 and owner3 (owner2 should be filtered out)
    assert_eq!(
      filtered_owners.len(),
      2,
      "Expected 2 filtered owners, got {}",
      filtered_owners.len()
    );
    assert!(filtered_owners.contains(&owner1), "Should contain owner1");
    assert!(filtered_owners.contains(&owner3), "Should contain owner3");
    assert!(
      !filtered_owners.contains(&owner2),
      "Should not contain owner2 (filtered out)"
    );
  }
}
