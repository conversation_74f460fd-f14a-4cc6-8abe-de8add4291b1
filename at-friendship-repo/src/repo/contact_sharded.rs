use {crate::repo::po::contacts::ContactPo,
     async_trait::async_trait,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::{ContactEntity,
                                       ContactOwnerId,
                                       ContactRepo,
                                       FriendshipError,
                                       FriendshipResult,
                                       PhoneNumber},
     sqlx::{MySql,
            Pool,
            Row},
     std::collections::HashMap,
     tracing::{debug,
               error}};

// Sharded contact repository implementation
pub struct ContactShardedRepoImpl {
  shard_manager: shard_manager::ContactShardManager,
}

impl ContactShardedRepoImpl {
  pub fn new(pools: Vec<Pool<MySql>>) -> Self {
    Self {
      shard_manager: shard_manager::ContactShardManager::new(pools),
    }
  }

  fn group_contacts_by_shard<'a>(&self, contacts: &'a [ContactEntity]) -> HashMap<usize, Vec<&'a ContactEntity>> {
    let mut groups = HashMap::new();
    for contact in contacts {
      let shard_index = self.shard_manager.get_shard_index(&contact.phone_number);
      groups.entry(shard_index).or_insert_with(Vec::new).push(contact);
    }
    groups
  }
}

#[async_trait]
impl ContactRepo for ContactShardedRepoImpl {
  #[tracing::instrument(skip(self, owner, contacts))]
  async fn update_contacts_for_owner_idempotentially(
    &self,
    owner: &ContactOwnerId,
    contacts: &[ContactEntity],
  ) -> FriendshipResult<()> {
    if contacts.is_empty() {
      return Ok(());
    }

    let shard_groups = self.group_contacts_by_shard(contacts);
    for (shard_index, shard_contacts) in shard_groups {
      let pool = self.shard_manager.get_pool_by_index(shard_index);
      let mut conn = pool.acquire().await.map_err(|e| {
        error!("Failed to acquire connection from shard {}: {:?}", shard_index, e);
        FriendshipError::DatabaseError(e.to_string())
      })?;

      let placeholders = (0 .. shard_contacts.len())
        .map(|_| "(?, ?, ?, ?, ?, ?)")
        .collect::<Vec<_>>()
        .join(", ");

      let query = format!(
        "INSERT INTO t_contacts (contact_id, owner_id, phone_number, contact_name, status, updated_at) VALUES \
         {placeholders} ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at), contact_name = VALUES(contact_name), \
         status = VALUES(status), phone_number = VALUES(phone_number)"
      );

      let query_builder = shard_contacts.iter().fold(sqlx::query(&query), |builder, contact| {
        builder
          .bind(contact.contact_id.inner_ref().inner_ref().to_vec())
          .bind(owner.inner_ref().inner_ref().inner_ref().to_vec())
          .bind(*contact.phone_number.inner_ref())
          .bind(contact.contact_name.as_ref().map(|n| n.inner_ref().to_string()))
          .bind(contact.status.code())
          .bind(contact.updated_at.inner_ref())
      });

      let result = query_builder.execute(&mut *conn).await.map_err(|e| {
        error!("Error executing query on shard {}: {:?}", shard_index, e);
        FriendshipError::DatabaseError(e.to_string())
      })?;

      if result.rows_affected() == 0 {
        error!(
          "Failed to update contacts for owner: {:?} on shard {}",
          owner, shard_index
        );
        return Err(FriendshipError::DatabaseError(sqlx::Error::RowNotFound.to_string()));
      }

      debug!("Updated {} contacts on shard {}", shard_contacts.len(), shard_index);
    }

    Ok(())
  }

  #[tracing::instrument(skip(self, owner_id, phone_number_filters))]
  async fn send_save_uid_notification(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<()> {
    debug!(
      "Sending save uid notification to {} phone numbers for owner: {:?}",
      phone_number_filters.len(),
      owner_id
    );
    Ok(())
  }

  #[tracing::instrument(skip(self, phone_number))]
  async fn get_contact_owners_with_friend_phone_number(
    &self,
    phone_number: &PhoneNumber,
  ) -> FriendshipResult<Vec<ContactOwnerId>> {
    let pool = self.shard_manager.get_pool(phone_number);
    let mut conn = pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection from shard: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let query = "SELECT contact_id, owner_id, phone_number, contact_name, status, created_at, updated_at 
                 FROM t_contacts WHERE phone_number = ? AND status = 1";

    let result = sqlx::query_as::<_, ContactPo>(query)
      .bind(*phone_number.inner_ref())
      .fetch_all(&mut *conn)
      .await
      .map_err(|e| {
        error!("Failed to fetch contact owners from shard: {:?}", e);
        FriendshipError::DatabaseError(e.to_string())
      })?;

    let owners: Vec<ContactOwnerId> = result
      .into_iter()
      .filter_map(|po| match po.to_contact_entity() {
        Ok(entity) => Some(entity.owner_id),
        Err(e) => {
          error!("Failed to convert contact entity: {:?}", e);
          None
        }
      })
      .collect();

    debug!(
      "Found {} contact owners for phone number: {}",
      owners.len(),
      phone_number
    );
    Ok(owners)
  }

  // get contact owners with friend phone number and owner ids
  async fn get_contact_owners_with_friend_phone_number_and_owner_ids(
    &self,
    phone_number: &PhoneNumber,
    owner_ids: &[ContactOwnerId],
  ) -> FriendshipResult<Vec<ContactOwnerId>> {
    if owner_ids.is_empty() {
      return Ok(vec![]);
    }

    let pool = self.shard_manager.get_pool(phone_number);
    let mut conn = pool.acquire().await.map_err(|e| {
      error!("Failed to acquire connection from shard: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let placeholders = owner_ids.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
    let query = format!(
      "SELECT contact_id, owner_id, phone_number, contact_name, status, created_at, updated_at FROM t_contacts WHERE \
       phone_number = ? AND owner_id IN ({placeholders}) AND status = 1"
    );

    let query_builder = owner_ids.iter().fold(
      sqlx::query_as::<_, ContactPo>(&query).bind(*phone_number.inner_ref()),
      |builder, owner_id| builder.bind(owner_id.inner_ref().inner_ref().inner_ref().to_vec()),
    );

    let result = query_builder.fetch_all(&mut *conn).await.map_err(|e| {
      error!("Failed to fetch contact owners from shard: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;

    let owners: Vec<ContactOwnerId> = result
      .into_iter()
      .filter_map(|po| match po.to_contact_entity() {
        Ok(entity) => Some(entity.owner_id),
        Err(e) => {
          error!("Failed to convert contact entity: {:?}", e);
          None
        }
      })
      .collect();

    debug!(
      "Found {} contact owners for phone number: {} and owner ids: {}",
      owners.len(),
      phone_number,
      owner_ids.len()
    );

    Ok(owners)
  }

  #[tracing::instrument(skip(self, owner_id, phone_number_filters))]
  async fn get_existing_contact_phone_numbers(
    &self,
    owner_id: &ContactOwnerId,
    phone_number_filters: &[PhoneNumber],
  ) -> FriendshipResult<Vec<PhoneNumber>> {
    if phone_number_filters.is_empty() {
      debug!("No phone number filters provided, returning empty result");
      return Ok(Vec::new());
    }

    // Group phone numbers by shard
    let mut shard_groups: HashMap<usize, Vec<&PhoneNumber>> = HashMap::new();
    for phone_number in phone_number_filters {
      let shard_index = self.shard_manager.get_shard_index(phone_number);
      shard_groups.entry(shard_index).or_default().push(phone_number);
    }

    let shard_count = shard_groups.len();

    // Query each shard in parallel using futures::join_all
    let shard_futures: Vec<_> = shard_groups
      .into_iter()
      .map(|(shard_index, shard_phone_numbers)| {
        let pool = self.shard_manager.get_pool_by_index(shard_index);
        let owner_id = owner_id.clone();

        async move {
          let mut conn = pool.acquire().await.map_err(|e| {
            error!("Failed to acquire connection from shard {}: {:?}", shard_index, e);
            FriendshipError::DatabaseError(e.to_string())
          })?;

          let placeholders = shard_phone_numbers.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
          let query = format!(
            "SELECT phone_number FROM t_contacts WHERE owner_id = ? AND phone_number IN ({placeholders}) AND status = \
             1"
          );

          let query_builder = shard_phone_numbers.iter().fold(
            sqlx::query(&query).bind(owner_id.inner_ref().inner_ref().inner_ref().to_vec()),
            |builder, phone_number| builder.bind(*phone_number.inner_ref()),
          );

          let result = query_builder.fetch_all(&mut *conn).await.map_err(|e| {
            error!(
              "Failed to get existing contact phone numbers from shard {}: {:?}",
              shard_index, e
            );
            FriendshipError::DatabaseError(e.to_string())
          })?;

          let shard_phone_numbers: Vec<PhoneNumber> = result
            .into_iter()
            .filter_map(|row| match row.try_get::<u64, _>("phone_number") {
              Ok(phone_number) => Some(PhoneNumber::new(phone_number)),
              Err(e) => {
                error!("Failed to extract phone number from row: {:?}", e);
                None
              }
            })
            .collect();

          Ok::<Vec<PhoneNumber>, FriendshipError>(shard_phone_numbers)
        }
      })
      .collect();

    // Execute all shard queries in parallel
    let shard_results = futures::future::join_all(shard_futures).await;

    // Collect results and handle errors
    let mut all_existing_phone_numbers = Vec::new();
    for result in shard_results {
      match result {
        Ok(phone_numbers) => all_existing_phone_numbers.extend(phone_numbers),
        Err(e) => return Err(e),
      }
    }

    debug!(
      "Found {} existing contact phone numbers for owner {:?} out of {} filters across {} shards",
      all_existing_phone_numbers.len(),
      owner_id,
      phone_number_filters.len(),
      shard_count
    );

    Ok(all_existing_phone_numbers)
  }
}

#[cfg(test)]
mod tests {
  use {super::*,
       at_friendship_domain_model::all::*,
       at_internal_deps::at_friendship::*,
       at_lib_comm_domain::{id::uuid::UuidBytes,
                            value::time::{CreateAt,
                                          UpdateAt}},
       chrono::Utc,
       sqlx::mysql::MySqlPoolOptions};

  async fn create_test_pools() -> Result<Vec<Pool<MySql>>, sqlx::Error> {
    let mut pools = Vec::new();
    for i in 1 ..= 16 {
      let db_url =
        format!("mysql://friendship_{i:02}user:friendship_{i:02}_c1sE2VXF5SgLyZ@**********:4000/friendship_{i:02}");
      let pool = MySqlPoolOptions::new().max_connections(2).connect(&db_url).await?;
      pools.push(pool);
    }
    Ok(pools)
  }

  fn create_test_contacts(owner: &ContactOwnerId) -> Vec<ContactEntity> {
    vec![
      ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner.clone(),
        phone_number: PhoneNumber::new(13800138000u64),
        contact_name: Some(ContactName::new("Test User".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::new(Utc::now()),
        updated_at:   UpdateAt::new(Utc::now()),
      },
      ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner.clone(),
        phone_number: PhoneNumber::new(13900139000u64),
        contact_name: Some(ContactName::new("Another User".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::new(Utc::now()),
        updated_at:   UpdateAt::new(Utc::now()),
      },
    ]
  }

  #[tokio::test]
  async fn test_sharded_operations() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let repo = ContactShardedRepoImpl::new(pools);
    let owner = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let contacts = create_test_contacts(&owner);

    // Test update operation
    if let Err(e) = repo.update_contacts_for_owner_idempotentially(&owner, &contacts).await {
      eprintln!("Update failed (expected if tables don't exist): {e}");
    }

    // Test query operation
    let phone = PhoneNumber::new(13800138000u64);
    if let Err(e) = repo.get_contact_owners_with_friend_phone_number(&phone).await {
      eprintln!("Query failed (expected if tables don't exist): {e}");
    }
  }

  #[tokio::test]
  async fn test_batch_insert_and_query() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let repo = ContactShardedRepoImpl::new(pools);

    // Create multiple owners with different contacts
    let owners = [
      ContactOwnerId::new(Auid::new(UuidBytes::generate())),
      ContactOwnerId::new(Auid::new(UuidBytes::generate())),
      ContactOwnerId::new(Auid::new(UuidBytes::generate())),
    ];

    let mut all_contacts = Vec::new();
    let mut phone_to_owner = HashMap::new();

    // Create contacts for each owner
    for (i, owner) in owners.iter().enumerate() {
      let phones = vec![
        13800138000u64 + i as u64,
        13900139000u64 + i as u64,
        15000150000u64 + i as u64,
      ];

      for phone in phones {
        let phone_number = PhoneNumber::new(phone);
        let contact = ContactEntity {
          contact_id:   ContactId::new(UuidBytes::generate()),
          owner_id:     owner.clone(),
          phone_number: phone_number.clone(),
          contact_name: Some(ContactName::new(format!("User {phone}"))),
          status:       ContactStatus::Normal,
          created_at:   CreateAt::new(Utc::now()),
          updated_at:   UpdateAt::new(Utc::now()),
        };

        phone_to_owner.insert(phone_number, owner.clone());
        all_contacts.push(contact);
      }
    }

    // Test batch insert
    for (owner, contacts) in owners.iter().zip(all_contacts.chunks(3)) {
      match repo.update_contacts_for_owner_idempotentially(owner, contacts).await {
        Ok(_) => println!("Successfully inserted {} contacts for owner", contacts.len()),
        Err(e) => eprintln!("Insert failed (expected if tables don't exist): {e}"),
      }
    }

    // Test individual queries
    for (phone_number, expected_owner) in phone_to_owner.iter().take(3) {
      match repo.get_contact_owners_with_friend_phone_number(phone_number).await {
        Ok(owners) => {
          println!("Found {} owners for phone {}", owners.len(), phone_number);
          if !owners.is_empty() {
            assert!(owners.contains(expected_owner), "Should find the expected owner");
          }
        }
        Err(e) => eprintln!("Query failed (expected if tables don't exist): {e}"),
      }
    }
  }

  #[tokio::test]
  async fn test_empty_and_edge_cases() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let repo = ContactShardedRepoImpl::new(pools);
    let owner = ContactOwnerId::new(Auid::new(UuidBytes::generate()));

    // Test empty contacts array
    let result = repo.update_contacts_for_owner_idempotentially(&owner, &[]).await;
    assert!(result.is_ok(), "Empty contacts should be handled gracefully");

    // Test query with non-existent phone
    let non_existent_phone = PhoneNumber::new(99999999999u64);
    match repo
      .get_contact_owners_with_friend_phone_number(&non_existent_phone)
      .await
    {
      Ok(owners) => {
        println!("Query for non-existent phone returned {} owners", owners.len());
        assert_eq!(owners.len(), 0, "Non-existent phone should return empty result");
      }
      Err(e) => eprintln!("Query failed (expected if tables don't exist): {e}"),
    }
  }

  #[tokio::test]
  async fn test_get_existing_contact_phone_numbers_across_shards() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let repo = ContactShardedRepoImpl::new(pools);
    let owner = ContactOwnerId::new(Auid::new(UuidBytes::generate()));

    // Create contacts with phone numbers that will go to different shards
    let test_phones = [
      PhoneNumber::new(13800138000u64), // Shard based on hash
      PhoneNumber::new(13900139000u64), // Different shard
      PhoneNumber::new(15000150000u64), // Another shard
    ];

    let contacts: Vec<ContactEntity> = test_phones
      .iter()
      .enumerate()
      .map(|(i, phone)| ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner.clone(),
        phone_number: phone.clone(),
        contact_name: Some(ContactName::new(format!("Test Contact {i}"))),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::now(),
        updated_at:   UpdateAt::now(),
      })
      .collect();

    // Insert contacts
    match repo.update_contacts_for_owner_idempotentially(&owner, &contacts).await {
      Ok(_) => println!("Successfully inserted {} contacts across shards", contacts.len()),
      Err(e) => {
        eprintln!("Insert failed (expected if tables don't exist): {e}");
        return;
      }
    }

    // Test get_existing_contact_phone_numbers with mixed existing and non-existing phones
    let query_phones = vec![
      PhoneNumber::new(13800138000u64), // Should exist
      PhoneNumber::new(13900139000u64), // Should exist
      PhoneNumber::new(99999999999u64), // Should not exist
    ];

    match repo.get_existing_contact_phone_numbers(&owner, &query_phones).await {
      Ok(existing_phones) => {
        println!(
          "Found {} existing phones out of {} queried",
          existing_phones.len(),
          query_phones.len()
        );
        assert!(existing_phones.len() >= 2, "Should find at least 2 existing phones");
        assert!(
          existing_phones.contains(&PhoneNumber::new(13800138000u64)),
          "Should contain first phone"
        );
        assert!(
          existing_phones.contains(&PhoneNumber::new(13900139000u64)),
          "Should contain second phone"
        );
        assert!(
          !existing_phones.contains(&PhoneNumber::new(99999999999u64)),
          "Should not contain non-existent phone"
        );
      }
      Err(e) => eprintln!("Query failed (expected if tables don't exist): {e}"),
    }
  }

  #[tokio::test]
  async fn test_get_contact_owners_with_friend_phone_number_and_owner_ids() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let repo = ContactShardedRepoImpl::new(pools);

    // Create multiple owners
    let owner1 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let owner2 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let owner3 = ContactOwnerId::new(Auid::new(UuidBytes::generate()));
    let shared_phone = PhoneNumber::new(13700137777u64);

    // Create contacts for all owners with the same phone number
    let contacts = vec![
      ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner1.clone(),
        phone_number: shared_phone.clone(),
        contact_name: Some(ContactName::new("Shared Contact 1".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::now(),
        updated_at:   UpdateAt::now(),
      },
      ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner2.clone(),
        phone_number: shared_phone.clone(),
        contact_name: Some(ContactName::new("Shared Contact 2".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::now(),
        updated_at:   UpdateAt::now(),
      },
      ContactEntity {
        contact_id:   ContactId::new(UuidBytes::generate()),
        owner_id:     owner3.clone(),
        phone_number: shared_phone.clone(),
        contact_name: Some(ContactName::new("Shared Contact 3".to_string())),
        status:       ContactStatus::Normal,
        created_at:   CreateAt::now(),
        updated_at:   UpdateAt::now(),
      },
    ];

    // Insert all contacts
    for contact in &contacts {
      match repo
        .update_contacts_for_owner_idempotentially(&contact.owner_id, &[contact.clone()])
        .await
      {
        Ok(_) => {}
        Err(e) => {
          eprintln!("Insert failed (expected if tables don't exist): {e}");
          return;
        }
      }
    }

    // Test filtering by specific owner IDs
    let filter_owners = vec![owner1.clone(), owner3.clone()]; // Only include owner1 and owner3

    match repo
      .get_contact_owners_with_friend_phone_number_and_owner_ids(&shared_phone, &filter_owners)
      .await
    {
      Ok(filtered_owners) => {
        println!(
          "Found {} filtered owners out of {} total",
          filtered_owners.len(),
          filter_owners.len()
        );
        assert!(filtered_owners.len() <= 2, "Should find at most 2 filtered owners");

        // Verify that only the requested owners are returned
        for owner in &filtered_owners {
          assert!(filter_owners.contains(owner), "Returned owner should be in filter list");
        }

        // If data was successfully inserted, we should find both owners
        if filtered_owners.len() == 2 {
          assert!(filtered_owners.contains(&owner1), "Should contain owner1");
          assert!(filtered_owners.contains(&owner3), "Should contain owner3");
          assert!(
            !filtered_owners.contains(&owner2),
            "Should not contain owner2 (filtered out)"
          );
        }
      }
      Err(e) => eprintln!("Query failed (expected if tables don't exist): {e}"),
    }
  }
}

mod shard_manager;
pub use shard_manager::ContactShardManager;
