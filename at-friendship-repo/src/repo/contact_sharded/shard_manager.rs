use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::PhoneN<PERSON>ber,
     sqlx::{MySql,
            Pool},
     std::{collections::{<PERSON>reeMap,
                         hash_map::DefaultHasher},
           hash::{Hash,
                  Hasher}},
     tracing::debug};

/// Consistent hash-based shard manager
pub struct ContactShardManager {
  pools:     Vec<Pool<MySql>>,
  hash_ring: BTreeMap<u64, usize>,
}

impl ContactShardManager {
  const VIRTUAL_NODES_PER_SHARD: usize = 150;

  pub fn new(pools: Vec<Pool<MySql>>) -> Self {
    // Create virtual nodes for load balancing using functional programming
    let hash_ring: BTreeMap<u64, usize> = pools
      .iter()
      .enumerate()
      .flat_map(|(shard_index, _)| {
        (0 .. Self::VIRTUAL_NODES_PER_SHARD).map(move |virtual_node| {
          let virtual_key = format!("shard_{shard_index}_vnode_{virtual_node}");
          let hash = Self::hash_key(&virtual_key);
          (hash, shard_index)
        })
      })
      .collect();

    debug!(
      "Created hash ring with {} virtual nodes for {} shards",
      hash_ring.len(),
      pools.len()
    );

    Self { pools, hash_ring }
  }

  fn hash_key<T: Hash>(key: &T) -> u64 {
    let mut hasher = DefaultHasher::new();
    key.hash(&mut hasher);
    hasher.finish()
  }

  pub fn get_shard_index(&self, phone_number: &PhoneNumber) -> usize {
    let hash = Self::hash_key(phone_number.inner_ref());

    self
      .hash_ring
      .range(hash ..)
      .next()
      .or_else(|| self.hash_ring.iter().next())
      .map(|(_, &shard_index)| shard_index)
      .unwrap_or(0)
  }

  pub fn get_pool(&self, phone_number: &PhoneNumber) -> &Pool<MySql> {
    let shard_index = self.get_shard_index(phone_number);
    &self.pools[shard_index]
  }

  pub fn get_pool_by_index(&self, shard_index: usize) -> &Pool<MySql> {
    &self.pools[shard_index]
  }

  pub fn get_all_pools(&self) -> &Vec<Pool<MySql>> {
    &self.pools
  }

  pub fn shard_count(&self) -> usize {
    self.pools.len()
  }
}

#[cfg(test)]
mod tests {
  use {super::*,
       at_friendship_domain_model::all::PhoneNumber,
       sqlx::mysql::MySqlPoolOptions,
       std::collections::HashMap};

  async fn create_test_pools() -> Result<Vec<Pool<MySql>>, sqlx::Error> {
    let mut pools = Vec::new();
    for i in 1 ..= 16 {
      let db_url =
        format!("mysql://friendship_{i:02}user:friendship_{i:02}_c1sE2VXF5SgLyZ@**********:4000/friendship_{i:02}");
      let pool = MySqlPoolOptions::new().max_connections(2).connect(&db_url).await?;
      pools.push(pool);
    }
    Ok(pools)
  }

  #[tokio::test]
  async fn test_consistent_hash_distribution() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let manager = ContactShardManager::new(pools);

    // Test distribution with sample phone numbers
    let phones = vec![
      PhoneNumber::new(13800138000u64),
      PhoneNumber::new(13900139000u64),
      PhoneNumber::new(15000150000u64),
      PhoneNumber::new(18600186000u64),
    ];

    let mut shard_counts = HashMap::new();
    for phone in &phones {
      let shard = manager.get_shard_index(phone);
      *shard_counts.entry(shard).or_insert(0) += 1;
    }

    println!("Shard distribution: {shard_counts:?}");
    assert!(shard_counts.len() > 1, "Should distribute across multiple shards");
  }

  #[tokio::test]
  async fn test_shard_consistency() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let manager = ContactShardManager::new(pools);

    // Test that same phone number always routes to same shard
    let test_phone = PhoneNumber::new(13800138000u64);

    let shard1 = manager.get_shard_index(&test_phone);
    let shard2 = manager.get_shard_index(&test_phone);
    let shard3 = manager.get_shard_index(&test_phone);

    assert_eq!(shard1, shard2, "Same phone should always route to same shard");
    assert_eq!(shard2, shard3, "Same phone should always route to same shard");

    // Test that different phones may route to different shards
    let different_phones = vec![
      PhoneNumber::new(13800138000u64),
      PhoneNumber::new(13900139000u64),
      PhoneNumber::new(15000150000u64),
    ];

    let mut unique_shards = std::collections::HashSet::new();
    for phone in &different_phones {
      let shard = manager.get_shard_index(phone);
      unique_shards.insert(shard);
      println!("Phone {phone} -> Shard {shard}");
    }

    println!("Total unique shards used: {}", unique_shards.len());
  }

  #[tokio::test]
  async fn test_cross_shard_data_distribution() {
    let pools = match create_test_pools().await {
      Ok(pools) => pools,
      Err(_) => {
        eprintln!("Skipping test - database not available");
        return;
      }
    };

    let manager = ContactShardManager::new(pools);

    // Create contacts with different phone numbers to ensure cross-shard distribution
    let test_phones: Vec<u64> = vec![
      13800138000,
      13900139000,
      15000150000,
      18600186000,
      17700177000,
      13300133000,
      18800188000,
      15500155000,
    ];

    let mut shard_distribution = HashMap::new();

    for phone in &test_phones {
      let phone_number = PhoneNumber::new(*phone);
      let shard_index = manager.get_shard_index(&phone_number);
      let count = shard_distribution.entry(shard_index).or_insert(0);
      *count += 1;

      println!("Phone {phone} -> Shard {shard_index}");
    }

    println!("Shard distribution: {shard_distribution:?}");

    // Verify data is distributed across multiple shards
    assert!(
      shard_distribution.len() > 1,
      "Data should be distributed across multiple shards"
    );

    // Verify all shards are within valid range
    for &shard_index in shard_distribution.keys() {
      assert!(
        shard_index < manager.shard_count(),
        "Shard index should be within valid range"
      );
    }
  }
}
