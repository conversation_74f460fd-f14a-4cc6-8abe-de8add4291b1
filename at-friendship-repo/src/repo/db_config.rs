//! Database configuration and connection pool optimization
//!
//! This module provides optimized database connection pool configurations
//! and utilities for different environments and workloads.

use {sqlx::{mysql::{MySqlConnectOptions,
                    MySqlPoolOptions},
            MySql,
            Pool},
     std::time::Duration,
     tracing::{info,
               warn}};

/// Database pool configuration
#[derive(Debug, Clone)]
pub struct DbPoolConfig {
  /// Maximum number of connections in the pool
  pub max_connections: u32,
  /// Minimum number of connections to maintain
  pub min_connections: u32,
  /// Maximum lifetime of a connection
  pub max_lifetime: Duration,
  /// Maximum idle time before closing a connection
  pub idle_timeout: Duration,
  /// Connection timeout
  pub connect_timeout: Duration,
  /// Statement timeout
  pub statement_timeout: Duration,
  /// Whether to test connections before use
  pub test_before_acquire: bool,
}

impl DbPoolConfig {
  /// Create configuration for production environment
  pub fn production() -> Self {
    Self {
      max_connections: 50,
      min_connections: 5,
      max_lifetime: Duration::from_secs(1800), // 30 minutes
      idle_timeout: Duration::from_secs(600),  // 10 minutes
      connect_timeout: Duration::from_secs(30),
      statement_timeout: Duration::from_secs(60),
      test_before_acquire: true,
    }
  }

  /// Create configuration for development environment
  pub fn development() -> Self {
    Self {
      max_connections: 10,
      min_connections: 1,
      max_lifetime: Duration::from_secs(3600), // 1 hour
      idle_timeout: Duration::from_secs(300),  // 5 minutes
      connect_timeout: Duration::from_secs(10),
      statement_timeout: Duration::from_secs(30),
      test_before_acquire: false,
    }
  }

  /// Create configuration for testing environment
  pub fn testing() -> Self {
    Self {
      max_connections: 5,
      min_connections: 1,
      max_lifetime: Duration::from_secs(300), // 5 minutes
      idle_timeout: Duration::from_secs(60),  // 1 minute
      connect_timeout: Duration::from_secs(5),
      statement_timeout: Duration::from_secs(10),
      test_before_acquire: false,
    }
  }

  /// Create configuration for high-throughput workloads
  pub fn high_throughput() -> Self {
    Self {
      max_connections: 100,
      min_connections: 10,
      max_lifetime: Duration::from_secs(900), // 15 minutes
      idle_timeout: Duration::from_secs(300), // 5 minutes
      connect_timeout: Duration::from_secs(15),
      statement_timeout: Duration::from_secs(30),
      test_before_acquire: true,
    }
  }

  /// Create configuration for low-latency workloads
  pub fn low_latency() -> Self {
    Self {
      max_connections: 20,
      min_connections: 10,
      max_lifetime: Duration::from_secs(3600), // 1 hour
      idle_timeout: Duration::from_secs(1800), // 30 minutes
      connect_timeout: Duration::from_secs(5),
      statement_timeout: Duration::from_secs(10),
      test_before_acquire: false, // Skip test for lower latency
    }
  }
}

/// Database connection builder with optimized settings
pub struct DbConnectionBuilder;

impl DbConnectionBuilder {
  /// Create an optimized MySQL connection pool
  pub async fn create_mysql_pool(
    database_url: &str,
    config: DbPoolConfig,
  ) -> Result<Pool<MySql>, sqlx::Error> {
    info!("Creating MySQL connection pool with config: {:?}", config);

    // Parse connection options from URL
    let mut connect_options = database_url.parse::<MySqlConnectOptions>()?;

    // Apply optimizations
    connect_options = connect_options
      .charset("utf8mb4")
      .collation("utf8mb4_unicode_ci")
      // Enable SSL if available
      .ssl_mode(sqlx::mysql::MySqlSslMode::Preferred)
      // Optimize for performance
      .statement_cache_capacity(100)
      // Set connection timeout
      .connect_timeout(config.connect_timeout);

    // Create pool with optimized settings
    let pool = MySqlPoolOptions::new()
      .max_connections(config.max_connections)
      .min_connections(config.min_connections)
      .max_lifetime(Some(config.max_lifetime))
      .idle_timeout(Some(config.idle_timeout))
      .test_before_acquire(config.test_before_acquire)
      .connect_with(connect_options)
      .await?;

    info!(
      "MySQL connection pool created successfully with {} max connections",
      config.max_connections
    );

    Ok(pool)
  }

  /// Create multiple sharded connection pools
  pub async fn create_sharded_pools(
    shard_configs: Vec<(String, DbPoolConfig)>,
  ) -> Result<Vec<Pool<MySql>>, sqlx::Error> {
    let mut pools = Vec::with_capacity(shard_configs.len());

    for (i, (database_url, config)) in shard_configs.into_iter().enumerate() {
      info!("Creating shard {} connection pool", i);
      
      match Self::create_mysql_pool(&database_url, config).await {
        Ok(pool) => {
          pools.push(pool);
          info!("Shard {} connection pool created successfully", i);
        }
        Err(e) => {
          warn!("Failed to create shard {} connection pool: {:?}", i, e);
          return Err(e);
        }
      }
    }

    info!("All {} shard connection pools created successfully", pools.len());
    Ok(pools)
  }

  /// Test connection pool health
  pub async fn test_pool_health(pool: &Pool<MySql>) -> Result<(), sqlx::Error> {
    let start = std::time::Instant::now();
    
    // Test basic connectivity
    let mut conn = pool.acquire().await?;
    sqlx::query("SELECT 1").fetch_one(&mut *conn).await?;
    
    let duration = start.elapsed();
    info!("Pool health check passed in {:?}", duration);
    
    if duration > Duration::from_millis(100) {
      warn!("Pool health check took longer than expected: {:?}", duration);
    }
    
    Ok(())
  }

  /// Get pool statistics
  pub fn get_pool_stats(pool: &Pool<MySql>) -> PoolStats {
    PoolStats {
      size: pool.size(),
      idle: pool.num_idle(),
      active: pool.size() - pool.num_idle(),
    }
  }
}

/// Pool statistics
#[derive(Debug, Clone)]
pub struct PoolStats {
  pub size: u32,
  pub idle: u32,
  pub active: u32,
}

impl PoolStats {
  /// Calculate pool utilization percentage
  pub fn utilization_percentage(&self) -> f64 {
    if self.size == 0 {
      0.0
    } else {
      (self.active as f64 / self.size as f64) * 100.0
    }
  }

  /// Check if pool is under high load
  pub fn is_high_load(&self) -> bool {
    self.utilization_percentage() > 80.0
  }

  /// Check if pool is underutilized
  pub fn is_underutilized(&self) -> bool {
    self.utilization_percentage() < 20.0 && self.size > 5
  }
}

/// Pool monitoring utilities
pub struct PoolMonitor;

impl PoolMonitor {
  /// Monitor pool health and log warnings
  pub async fn monitor_pool_health(pool: &Pool<MySql>, pool_name: &str) {
    let stats = DbConnectionBuilder::get_pool_stats(pool);
    
    if stats.is_high_load() {
      warn!(
        "Pool {} is under high load: {:.1}% utilization ({} active / {} total)",
        pool_name,
        stats.utilization_percentage(),
        stats.active,
        stats.size
      );
    }
    
    if stats.is_underutilized() {
      info!(
        "Pool {} is underutilized: {:.1}% utilization ({} active / {} total)",
        pool_name,
        stats.utilization_percentage(),
        stats.active,
        stats.size
      );
    }
    
    // Test connectivity
    if let Err(e) = DbConnectionBuilder::test_pool_health(pool).await {
      warn!("Pool {} health check failed: {:?}", pool_name, e);
    }
  }

  /// Start background monitoring task
  pub fn start_monitoring(pool: Pool<MySql>, pool_name: String, interval: Duration) -> tokio::task::JoinHandle<()> {
    tokio::spawn(async move {
      let mut interval_timer = tokio::time::interval(interval);
      
      loop {
        interval_timer.tick().await;
        Self::monitor_pool_health(&pool, &pool_name).await;
      }
    })
  }
}

/// Environment detection utilities
pub struct EnvironmentDetector;

impl EnvironmentDetector {
  /// Detect current environment and return appropriate config
  pub fn detect_and_configure() -> DbPoolConfig {
    match std::env::var("ENVIRONMENT").as_deref() {
      Ok("production") | Ok("prod") => {
        info!("Detected production environment, using production DB config");
        DbPoolConfig::production()
      }
      Ok("development") | Ok("dev") => {
        info!("Detected development environment, using development DB config");
        DbPoolConfig::development()
      }
      Ok("test") | Ok("testing") => {
        info!("Detected testing environment, using testing DB config");
        DbPoolConfig::testing()
      }
      Ok("high-throughput") => {
        info!("Detected high-throughput environment, using high-throughput DB config");
        DbPoolConfig::high_throughput()
      }
      Ok("low-latency") => {
        info!("Detected low-latency environment, using low-latency DB config");
        DbPoolConfig::low_latency()
      }
      _ => {
        warn!("Unknown or unset environment, defaulting to development DB config");
        DbPoolConfig::development()
      }
    }
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_pool_stats_utilization() {
    let stats = PoolStats {
      size: 10,
      idle: 2,
      active: 8,
    };
    
    assert_eq!(stats.utilization_percentage(), 80.0);
    assert!(!stats.is_high_load()); // 80% is not > 80%
    assert!(!stats.is_underutilized());
  }

  #[test]
  fn test_pool_stats_high_load() {
    let stats = PoolStats {
      size: 10,
      idle: 1,
      active: 9,
    };
    
    assert_eq!(stats.utilization_percentage(), 90.0);
    assert!(stats.is_high_load());
    assert!(!stats.is_underutilized());
  }

  #[test]
  fn test_pool_stats_underutilized() {
    let stats = PoolStats {
      size: 10,
      idle: 9,
      active: 1,
    };
    
    assert_eq!(stats.utilization_percentage(), 10.0);
    assert!(!stats.is_high_load());
    assert!(stats.is_underutilized());
  }

  #[test]
  fn test_environment_configs() {
    let prod_config = DbPoolConfig::production();
    assert_eq!(prod_config.max_connections, 50);
    assert!(prod_config.test_before_acquire);

    let dev_config = DbPoolConfig::development();
    assert_eq!(dev_config.max_connections, 10);
    assert!(!dev_config.test_before_acquire);

    let test_config = DbPoolConfig::testing();
    assert_eq!(test_config.max_connections, 5);
  }
}
