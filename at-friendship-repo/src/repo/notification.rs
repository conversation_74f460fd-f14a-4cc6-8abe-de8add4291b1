use {crate::repo::po::notification::NotificationPO,
     async_trait::async_trait,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_janus_client::prelude::*,
     at_msg_receiver_stub::prelude::*,
     std::sync::Arc,
     tracing::error};

pub struct NotificationRepoImpl {
  stub:      Arc<ReceiveP2PMessageStub>,
  semaphore: Arc<tokio::sync::Semaphore>,
}

impl NotificationRepoImpl {
  pub fn new(janus_client: JanusClient<EtcdServiceFetcher>) -> Self {
    Self {
      stub:      Arc::new(ReceiveP2PMessageStub::new(janus_client)),
      semaphore: Arc::new(tokio::sync::Semaphore::new(50)),
    }
  }
}

#[async_trait]
impl NotificationRepo for NotificationRepoImpl {
  #[tracing::instrument(name = "push_notification", skip(self, notification))]
  async fn push_notification(&self, notification: &BotimUserNotificationEntity) -> FriendshipResult<()> {
    let msg_entity_list = NotificationPO::to_message_entity_list(notification)
      .map_err(|e| FriendshipError::PushNotification(format!("Failed to covert notification proto: {e:?}")))?;

    let stub = self.stub.clone();
    let semaphore = self.semaphore.clone();

    tokio::spawn(async move {
      let futures = msg_entity_list.msg_entity_list.iter().map(|msg_entity| {
        let stub = stub.clone();
        let semaphore = semaphore.clone();
        let msg_entity = msg_entity.clone();
        async move {
          let _permit = match semaphore.acquire().await {
            Ok(permit) => permit,
            Err(e) => {
              error!("Failed to acquire semaphore permit: {:?}", e);
              return Err(anyhow::anyhow!("Semaphore acquire failed"));
            }
          };

          stub
            .receive_s2c_message("push_notification", "at-friendship", &msg_entity)
            .await
            .map_err(|e| anyhow::anyhow!("Push notification failed: {:?}", e))
        }
      });

      if let Err(e) = futures::future::join_all(futures)
        .await
        .into_iter()
        .collect::<Result<Vec<_>, _>>()
        .map(|_| ())
      {
        error!("Failed to push notification: {:?}", e);
      }
    });
    Ok(())
  }
}
