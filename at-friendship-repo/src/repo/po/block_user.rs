use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::{<PERSON><PERSON>,
                                       BlockUserStatus,
                                       BlockedUserEntity,
                                       Botim<PERSON><PERSON>Id,
                                       FriendshipError,
                                       FriendshipResult},
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::{id::uuid::UuidBytes,
                          value::time::{CreateAt,
                                        UpdateAt}},
     chrono::{DateTime,
              Utc},
     sqlx::FromRow};

#[derive(Debug, <PERSON>lone, FromRow)]
pub struct BlockedUserPo {
  user_id:         Vec<u8>,
  blocked_user_id: Vec<u8>,
  status:          u8,
  created_at:      DateTime<Utc>,
  updated_at:      DateTime<Utc>,
}

impl BlockedUserPo {
  pub fn new(
    user_id: Vec<u8>,
    blocked_user_id: Vec<u8>,
    status: u8,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
  ) -> Self {
    Self {
      user_id,
      blocked_user_id,
      status,
      created_at,
      updated_at,
    }
  }

  pub fn to_blocked_user_entity(&self) -> FriendshipResult<BlockedUserEntity> {
    let user_id: [u8; 16] = self
      .user_id
      .as_slice()
      .try_into()
      .map_err(|_| FriendshipError::InvalidUserId("invalid user id".to_string()))?;
    let blocked_user_id: [u8; 16] = self
      .blocked_user_id
      .as_slice()
      .try_into()
      .map_err(|_| FriendshipError::InvalidUserId("invalid blocked user id".to_string()))?;

    Ok(BlockedUserEntity {
      user_id:         BotimUserId::new(Auid::new(UuidBytes::new(user_id))),
      blocked_user_id: BotimUserId::new(Auid::new(UuidBytes::new(blocked_user_id))),
      status:          BlockUserStatus::from(self.status),
      created_at:      CreateAt::new(self.created_at),
      updated_at:      UpdateAt::new(self.updated_at),
    })
  }
}
