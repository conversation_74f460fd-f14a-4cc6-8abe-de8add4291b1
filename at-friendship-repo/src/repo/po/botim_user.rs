use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::{id::uuid::UuidBytes,
                          value::time::{CreateAt,
                                        UpdateAt}},
     chrono::{DateTime,
              Utc},
     sqlx::FromRow};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow)]
pub struct BotimUserPo {
  botim_user_id: Vec<u8>,
  auid_repr:     String,
  phone_number:  u64,
  created_at:    DateTime<Utc>,
  updated_at:    DateTime<Utc>,
}

impl BotimUserPo {
  pub fn new(
    botim_user_id: Vec<u8>,
    auid_repr: String,
    phone_number: u64,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
  ) -> Self {
    Self {
      botim_user_id,
      auid_repr,
      phone_number,
      created_at,
      updated_at,
    }
  }

  pub fn to_botim_user_entity(&self) -> FriendshipResult<BotimUserEntity> {
    let botim_user_id: [u8; 16] = self
      .botim_user_id
      .as_slice()
      .try_into()
      .map_err(|_| FriendshipError::InvalidUserId("invalid botim user id".to_string()))?;
    Ok(BotimUserEntity {
      botim_user_id: BotimUserId::new(Auid::new(UuidBytes::new(botim_user_id))),
      auid_repr:     AuidRepr::new(self.auid_repr.clone()),
      phone_number:  PhoneNumber::new(self.phone_number),
      created_at:    CreateAt::new(self.created_at),
      updated_at:    UpdateAt::new(self.updated_at),
    })
  }
}
