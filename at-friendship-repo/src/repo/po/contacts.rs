use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::{id::uuid::UuidBytes,
                          value::time::{CreateAt,
                                        UpdateAt}},
     chrono::{DateTime,
              Utc},
     sqlx::FromRow};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow)]
pub struct ContactPo {
  contact_id:   Vec<u8>,
  owner_id:     Vec<u8>,
  phone_number: u64,
  contact_name: Option<String>,
  status:       u8,
  created_at:   Option<DateTime<Utc>>,
  updated_at:   Option<DateTime<Utc>>,
}

impl ContactPo {
  pub fn new(
    contact_id: Vec<u8>,
    owner_id: Vec<u8>,
    phone_number: u64,
    contact_name: Option<String>,
    status: u8,
    created_at: Option<DateTime<Utc>>,
    updated_at: Option<DateTime<Utc>>,
  ) -> Self {
    Self {
      contact_id,
      owner_id,
      phone_number,
      contact_name,
      status,
      created_at,
      updated_at,
    }
  }

  pub fn to_contact_entity(&self) -> FriendshipResult<ContactEntity> {
    let contact_id: [u8; 16] = self
      .contact_id
      .as_slice()
      .try_into()
      .map_err(|_| FriendshipError::InvalidUserId("invalid contact id".to_string()))?;

    let owner_id: [u8; 16] = self
      .owner_id
      .as_slice()
      .try_into()
      .map_err(|_| FriendshipError::InvalidUserId("invalid user id".to_string()))?;

    let created_at = self.created_at.map_or(CreateAt::now(), CreateAt::new);
    let updated_at = self.updated_at.map_or(UpdateAt::now(), UpdateAt::new);

    Ok(ContactEntity {
      contact_id: ContactId::new(UuidBytes::new(contact_id)),
      owner_id: ContactOwnerId::new(Auid::new(UuidBytes::new(owner_id))),
      phone_number: PhoneNumber::new(self.phone_number),
      contact_name: self.contact_name.as_ref().map(|n| ContactName::new(n.clone())),
      status: ContactStatus::from(self.status),
      created_at,
      updated_at,
    })
  }
}
