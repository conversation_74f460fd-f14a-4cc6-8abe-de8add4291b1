use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::id::uuid::UuidBytes,
     at_msg_protocol_pb::{msg::{msg_content_pb,
                                receiving_end_pb,
                                sending_source_pb,
                                system_message_pb},
                          prelude::*}};
pub struct NotificationPO;

impl NotificationPO {
  // Create a single MsgEntityPb for a given receiver
  #[tracing::instrument(name = "create_message_entity", skip(notification, receiver))]
  pub fn create_message_entity(
    notification: &BotimUserNotificationEntity,
    receiver: &str,
  ) -> FriendshipResult<MsgEntityPb> {
    let status = match notification.status {
      RegisterEvent::Register => 0,
      RegisterEvent::Unregister => 1,
    };

    // Use the API structure from the correct dependency version
    let person_receiver = PersonReceiverPb {
      user_repr: receiver.to_owned(),
      device_id: None,
    };

    let msg_entity = MsgEntityPb {
      id:      UuidBytes::generate().inner_ref().to_vec(),
      meta:    Some(MsgMetaPb {
        sending_source: Some(SendingSourcePb {
          sender: Some(sending_source_pb::Sender::SystemSender(SystemSenderPb {
            system_repr:    "botim".to_string(),
            in_group_repr:  None,
            in_person_repr: None,
          })),
        }),
        receiving_end:  Some(ReceivingEndPb {
          receiver: Some(receiving_end_pb::Receiver::PersonReceiver(person_receiver)),
        }),
        msg_status:     MsgStatusPb::Sent as i32,
        create_at:      notification.created_at.unix_millis(),
        update_at:      notification.created_at.unix_millis(),
      }),
      content: Some(MsgContentPb {
        content: Some(msg_content_pb::Content::SystemMessage(SystemMessagePb {
          system: Some(system_message_pb::System::BotimUserNotification(
            BotimUserNotification {
              status,
              botim_user: notification.botim_user.as_ref().map(|user| BotimUserInfo {
                auid_repr:    user.auid_repr.inner_ref().to_string(),
                phone_number: user.phone_number.inner_ref().to_string(),
              }),
            },
          )),
        })),
      }),
    };

    Ok(msg_entity)
  }

  // Create a MsgEntityListPb containing entities for all receivers
  #[tracing::instrument(name = "to_message_entity_list", skip(notification))]
  pub fn to_message_entity_list(notification: &BotimUserNotificationEntity) -> FriendshipResult<MsgEntityListPb> {
    // Check if receiver list is empty
    if notification.receiver.is_empty() {
      return Err(FriendshipError::InvalidInput("No receivers found".to_string()));
    }

    // Create message entities for each receiver using functional approach
    let entities = notification
      .receiver
      .iter()
      .map(|receiver| receiver.inner_ref().to_string())
      .map(|receiver_str| Self::create_message_entity(notification, &receiver_str))
      .collect::<FriendshipResult<Vec<_>>>()?;

    Ok(MsgEntityListPb {
      msg_entity_list: entities,
    })
  }
}
