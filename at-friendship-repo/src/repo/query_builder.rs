//! Safe query builder utilities to prevent SQL injection
//!
//! This module provides utilities for building safe SQL queries with proper
//! parameter binding and input validation.

use {at_friendship_domain_model::all::FriendshipError,
     sqlx::{MySql,
            QueryBuilder}};

/// Maximum number of parameters allowed in a single IN clause
const MAX_IN_CLAUSE_PARAMS: usize = 1000;

/// Safe query builder for IN clauses
pub struct SafeInClauseBuilder;

impl SafeInClauseBuilder {
  /// Build a safe IN clause query with parameter validation
  ///
  /// # Arguments
  /// * `base_query` - The base SQL query with {} placeholder for IN clause
  /// * `param_count` - Number of parameters for the IN clause
  ///
  /// # Returns
  /// * `Ok(String)` - Safe SQL query with proper placeholders
  /// * `Err(FriendshipError)` - If parameter count exceeds safety limits
  ///
  /// # Example
  /// ```rust
  /// let query = SafeInClauseBuilder::build_in_query(
  ///   "SELECT * FROM users WHERE id IN ({})",
  ///   user_ids.len()
  /// )?;
  /// ```
  pub fn build_in_query(base_query: &str, param_count: usize) -> Result<String, FriendshipError> {
    if param_count == 0 {
      return Err(FriendshipError::InvalidInput(
        "Parameter count cannot be zero".to_string(),
      ));
    }

    if param_count > MAX_IN_CLAUSE_PARAMS {
      return Err(FriendshipError::InvalidInput(format!(
        "Too many parameters in IN clause: {} (max {})",
        param_count, MAX_IN_CLAUSE_PARAMS
      )));
    }

    let placeholders = (0..param_count).map(|_| "?").collect::<Vec<_>>().join(", ");
    Ok(base_query.replace("{}", &placeholders))
  }

  /// Build a safe query builder for MySQL with IN clause
  ///
  /// # Arguments
  /// * `base_query` - The base SQL query with {} placeholder for IN clause
  /// * `param_count` - Number of parameters for the IN clause
  ///
  /// # Returns
  /// * `Ok(QueryBuilder<MySql>)` - Safe query builder ready for parameter binding
  /// * `Err(FriendshipError)` - If parameter count exceeds safety limits
  pub fn build_mysql_query_builder(
    base_query: &str,
    param_count: usize,
  ) -> Result<QueryBuilder<MySql>, FriendshipError> {
    let query = Self::build_in_query(base_query, param_count)?;
    Ok(sqlx::QueryBuilder::new(query))
  }
}

/// Utility for validating query parameters
pub struct QueryValidator;

impl QueryValidator {
  /// Validate that a collection size is within safe limits
  pub fn validate_collection_size<T>(
    collection: &[T],
    max_size: usize,
    collection_name: &str,
  ) -> Result<(), FriendshipError> {
    if collection.is_empty() {
      return Err(FriendshipError::InvalidInput(format!(
        "{} cannot be empty",
        collection_name
      )));
    }

    if collection.len() > max_size {
      return Err(FriendshipError::InvalidInput(format!(
        "Too many items in {}: {} (max {})",
        collection_name,
        collection.len(),
        max_size
      )));
    }

    Ok(())
  }

  /// Validate phone number format (basic validation)
  pub fn validate_phone_number(phone_number: u64) -> Result<(), FriendshipError> {
    // Basic validation - phone numbers should be reasonable length
    if phone_number == 0 {
      return Err(FriendshipError::InvalidInput(
        "Phone number cannot be zero".to_string(),
      ));
    }

    // Check if phone number is within reasonable range (1-15 digits)
    let digit_count = phone_number.to_string().len();
    if digit_count < 7 || digit_count > 15 {
      return Err(FriendshipError::InvalidInput(format!(
        "Invalid phone number length: {} digits (expected 7-15)",
        digit_count
      )));
    }

    Ok(())
  }

  /// Validate string input for SQL safety
  pub fn validate_string_input(input: &str, max_length: usize, field_name: &str) -> Result<(), FriendshipError> {
    if input.is_empty() {
      return Err(FriendshipError::InvalidInput(format!(
        "{} cannot be empty",
        field_name
      )));
    }

    if input.len() > max_length {
      return Err(FriendshipError::InvalidInput(format!(
        "{} too long: {} characters (max {})",
        field_name,
        input.len(),
        max_length
      )));
    }

    // Check for potentially dangerous characters
    if input.contains('\0') {
      return Err(FriendshipError::InvalidInput(format!(
        "{} contains null character",
        field_name
      )));
    }

    Ok(())
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_build_in_query_success() {
    let query = SafeInClauseBuilder::build_in_query("SELECT * FROM users WHERE id IN ({})", 3).unwrap();
    assert_eq!(query, "SELECT * FROM users WHERE id IN (?, ?, ?)");
  }

  #[test]
  fn test_build_in_query_empty_params() {
    let result = SafeInClauseBuilder::build_in_query("SELECT * FROM users WHERE id IN ({})", 0);
    assert!(result.is_err());
  }

  #[test]
  fn test_build_in_query_too_many_params() {
    let result = SafeInClauseBuilder::build_in_query("SELECT * FROM users WHERE id IN ({})", 1001);
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_collection_size_success() {
    let collection = vec![1, 2, 3];
    let result = QueryValidator::validate_collection_size(&collection, 5, "test_collection");
    assert!(result.is_ok());
  }

  #[test]
  fn test_validate_collection_size_empty() {
    let collection: Vec<i32> = vec![];
    let result = QueryValidator::validate_collection_size(&collection, 5, "test_collection");
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_collection_size_too_large() {
    let collection = vec![1, 2, 3, 4, 5, 6];
    let result = QueryValidator::validate_collection_size(&collection, 5, "test_collection");
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_phone_number_success() {
    let result = QueryValidator::validate_phone_number(1234567890);
    assert!(result.is_ok());
  }

  #[test]
  fn test_validate_phone_number_zero() {
    let result = QueryValidator::validate_phone_number(0);
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_phone_number_too_short() {
    let result = QueryValidator::validate_phone_number(123456);
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_phone_number_too_long() {
    let result = QueryValidator::validate_phone_number(12345678901234567890);
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_string_input_success() {
    let result = QueryValidator::validate_string_input("valid_string", 20, "test_field");
    assert!(result.is_ok());
  }

  #[test]
  fn test_validate_string_input_empty() {
    let result = QueryValidator::validate_string_input("", 20, "test_field");
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_string_input_too_long() {
    let result = QueryValidator::validate_string_input("this_string_is_too_long", 10, "test_field");
    assert!(result.is_err());
  }

  #[test]
  fn test_validate_string_input_null_character() {
    let result = QueryValidator::validate_string_input("test\0string", 20, "test_field");
    assert!(result.is_err());
  }
}
