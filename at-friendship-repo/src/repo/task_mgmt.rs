use {async_trait::async_trait,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     fred::prelude::*,
     tracing::error};

pub struct TaskMgmtRepoImpl {
  pool: Pool,
}

impl TaskMgmtRepoImpl {
  pub fn new(pool: Pool) -> Self {
    Self { pool }
  }
}

#[async_trait]
impl TaskMgmtRepo for TaskMgmtRepoImpl {
  #[tracing::instrument(name = "add_task", skip(self, task_id))]
  async fn add_task(&self, task_id: &str) -> FriendshipResult<()> {
    let ttl = Expiration::EX(600);
    self
      .pool
      .set::<(), _, _>(task_id, "1", Some(ttl), None, false)
      .await
      .map_err(|e| FriendshipError::RedisError(e.to_string()))?;
    Ok(())
  }

  #[tracing::instrument(name = "get_task", skip(self, task_id))]
  async fn get_task(&self, task_id: &str) -> FriendshipResult<bool> {
    let value: Option<String> = self
      .pool
      .get::<Option<String>, _>(task_id)
      .await
      .map_err(|e| FriendshipError::DatabaseError(e.to_string()))?;
    Ok(value.is_some())
  }

  #[tracing::instrument(name = "delete_task", skip(self, task_id))]
  async fn delete_task(&self, task_id: &str) -> FriendshipResult<()> {
    let _: i64 = self.pool.del(task_id).await.map_err(|e| {
      error!("Failed to delete task: {:?}", e);
      FriendshipError::DatabaseError(e.to_string())
    })?;
    Ok(())
  }
}
