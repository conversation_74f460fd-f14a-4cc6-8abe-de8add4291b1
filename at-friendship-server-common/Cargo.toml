[package]
name = "at-friendship-server-common"
version.workspace = true
edition.workspace = true
authors.workspace = true
repository.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true

[dependencies]
# dep-mgmt
at-deps = { workspace = true }
at-internal-deps = { workspace = true }
at-janus-client = { workspace = true }

## internal dependencies
at-friendship-repo = { workspace = true }
at-friendship-domain-model = { workspace = true }
at-friendship-protocol-pb = { workspace = true }
at-friendship-integration = { workspace = true }
at-friendship-stub = { workspace = true }

## exceptional
serde = { workspace = true }
sqlx = { workspace = true }
tonic = { workspace = true }
futures = { workspace = true }
prost = { workspace = true }
fred = { workspace = true }
tracing = { workspace = true }