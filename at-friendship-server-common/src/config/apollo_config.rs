//! Apollo configuration integration
//!
//! This module provides utilities for loading configuration from Apollo
//! configuration center with proper error handling and validation.

use {super::{ConfigError,
             DatabaseConfig,
             Environment,
             LoggingConfig,
             PerformanceConfig,
             RedisConfig,
             SecurityConfig,
             ServerConfig},
     at_ac_integration::prelude::{AutoUpdateApolloConfigLoader,
                                  Configuration},
     std::time::Duration,
     tracing::{error,
               info,
               warn}};

/// Apollo configuration loader with enhanced error handling
pub struct ApolloConfigManager;

impl ApolloConfigManager {
  /// Load and validate Apollo configuration
  pub async fn load_configuration() -> Result<Configuration, ConfigError> {
    let config_loader = AutoUpdateApolloConfigLoader::app_singleton();
    
    // Auto-update configuration from Apollo
    config_loader.auto_update().await.map_err(|e| {
      error!("Failed to auto-update Apollo configuration: {:?}", e);
      ConfigError::ParseError(format!("Apollo auto-update failed: {}", e))
    })?;

    let app_config = config_loader.config_snapshot().await;
    let configuration = app_config.configuration();
    
    info!("Successfully loaded configuration from Apollo");
    Ok(configuration)
  }

  /// Get string value from Apollo configuration with validation
  pub fn get_string_required(
    config: &Configuration,
    key: &str,
  ) -> Result<String, ConfigError> {
    config
      .get_string(key)
      .map_err(|e| {
        error!("Failed to get string value for key '{}': {:?}", key, e);
        ConfigError::ParseError(format!("Failed to parse key '{}': {}", key, e))
      })?
      .ok_or_else(|| {
        warn!("Required configuration key '{}' is missing", key);
        ConfigError::MissingRequired(key.to_string())
      })
  }

  /// Get string value from Apollo configuration with default
  pub fn get_string_with_default(
    config: &Configuration,
    key: &str,
    default: &str,
  ) -> String {
    match config.get_string(key) {
      Ok(Some(value)) => value,
      Ok(None) => {
        info!("Configuration key '{}' not found, using default: '{}'", key, default);
        default.to_string()
      }
      Err(e) => {
        warn!("Failed to get configuration key '{}', using default '{}': {:?}", key, default, e);
        default.to_string()
      }
    }
  }

  /// Get integer value from Apollo configuration
  pub fn get_u32_required(
    config: &Configuration,
    key: &str,
  ) -> Result<u32, ConfigError> {
    let value_str = Self::get_string_required(config, key)?;
    value_str.parse::<u32>().map_err(|e| {
      error!("Failed to parse '{}' as u32 for key '{}': {:?}", value_str, key, e);
      ConfigError::InvalidValue(format!("Invalid u32 value for '{}': {}", key, value_str))
    })
  }

  /// Get integer value from Apollo configuration with default
  pub fn get_u32_with_default(
    config: &Configuration,
    key: &str,
    default: u32,
  ) -> u32 {
    match Self::get_string_with_default(config, key, &default.to_string()).parse::<u32>() {
      Ok(value) => value,
      Err(e) => {
        warn!("Failed to parse '{}' as u32, using default {}: {:?}", key, default, e);
        default
      }
    }
  }

  /// Get duration value from Apollo configuration (in seconds)
  pub fn get_duration_seconds_required(
    config: &Configuration,
    key: &str,
  ) -> Result<Duration, ConfigError> {
    let seconds = Self::get_u32_required(config, key)?;
    Ok(Duration::from_secs(seconds as u64))
  }

  /// Get duration value from Apollo configuration with default (in seconds)
  pub fn get_duration_seconds_with_default(
    config: &Configuration,
    key: &str,
    default_seconds: u64,
  ) -> Duration {
    let seconds = Self::get_u32_with_default(config, key, default_seconds as u32);
    Duration::from_secs(seconds as u64)
  }

  /// Get boolean value from Apollo configuration
  pub fn get_bool_with_default(
    config: &Configuration,
    key: &str,
    default: bool,
  ) -> bool {
    match Self::get_string_with_default(config, key, &default.to_string()).as_str() {
      "true" | "1" | "yes" | "on" => true,
      "false" | "0" | "no" | "off" => false,
      value => {
        warn!("Invalid boolean value '{}' for key '{}', using default {}", value, key, default);
        default
      }
    }
  }

  /// Get array of configurations (for sharded databases)
  pub fn get_array_required(
    config: &Configuration,
    key: &str,
  ) -> Result<Vec<serde_json::Value>, ConfigError> {
    config
      .get_array(key)
      .map_err(|e| {
        error!("Failed to get array value for key '{}': {:?}", key, e);
        ConfigError::ParseError(format!("Failed to parse array key '{}': {}", key, e))
      })?
      .ok_or_else(|| {
        warn!("Required array configuration key '{}' is missing", key);
        ConfigError::MissingRequired(key.to_string())
      })
  }

  /// Validate configuration completeness
  pub fn validate_required_keys(
    config: &Configuration,
    required_keys: &[&str],
  ) -> Result<(), ConfigError> {
    let mut missing_keys = Vec::new();

    for &key in required_keys {
      if config.get_string(key).unwrap_or(None).is_none() {
        missing_keys.push(key);
      }
    }

    if !missing_keys.is_empty() {
      return Err(ConfigError::MissingRequired(format!(
        "Missing required configuration keys: {}",
        missing_keys.join(", ")
      )));
    }

    Ok(())
  }
}

/// Configuration keys used in Apollo
pub struct ApolloKeys;

impl ApolloKeys {
  // Server configuration keys
  pub const SERVER_ADDRESS: &'static str = "server_address";
  pub const SERVER_HOST: &'static str = "server_host";
  pub const SERVER_PORT: &'static str = "server_port";
  pub const MAX_CONNECTIONS: &'static str = "max_connections";
  pub const REQUEST_TIMEOUT: &'static str = "request_timeout_seconds";
  pub const SHUTDOWN_TIMEOUT: &'static str = "shutdown_timeout_seconds";

  // Database configuration keys
  pub const FRIENDSHIP_DB_URL: &'static str = "friendship_db_url";
  pub const DB_MAX_CONNECTIONS: &'static str = "db_max_connections";
  pub const DB_MIN_CONNECTIONS: &'static str = "db_min_connections";
  pub const DB_CONNECTION_TIMEOUT: &'static str = "db_connection_timeout_seconds";
  pub const DB_IDLE_TIMEOUT: &'static str = "db_idle_timeout_seconds";
  pub const DB_MAX_LIFETIME: &'static str = "db_max_lifetime_seconds";
  pub const FRIENDSHIP_SHARDED_TIDB_CONFIGS: &'static str = "friendship_sharded_tidb_configs";

  // Redis configuration keys
  pub const FRIENDSHIP_REDIS_URL: &'static str = "friendship_redis_url";
  pub const REDIS_POOL_SIZE: &'static str = "redis_pool_size";
  pub const REDIS_CONNECTION_TIMEOUT: &'static str = "redis_connection_timeout_seconds";
  pub const REDIS_COMMAND_TIMEOUT: &'static str = "redis_command_timeout_seconds";

  // MQ configuration keys
  pub const RABBITMQ_URL: &'static str = "rabbitmq_url";
  pub const RABBITMQ_EXCHANGE: &'static str = "rabbitmq_exchange";
  pub const SERVICE_NAME: &'static str = "service_name";

  // Logging configuration keys
  pub const LOG_LEVEL: &'static str = "log_level";
  pub const LOG_FORMAT: &'static str = "log_format";
  pub const ENABLE_JSON_LOGGING: &'static str = "enable_json_logging";
  pub const ENABLE_CONSOLE_LOGGING: &'static str = "enable_console_logging";

  // Security configuration keys
  pub const ENABLE_AUTH: &'static str = "enable_auth";
  pub const JWT_SECRET: &'static str = "jwt_secret";
  pub const RATE_LIMIT_REQUESTS: &'static str = "rate_limit_requests";
  pub const RATE_LIMIT_WINDOW_SECONDS: &'static str = "rate_limit_window_seconds";
  pub const MAX_REQUEST_SIZE: &'static str = "max_request_size";

  // Performance configuration keys
  pub const CACHE_TTL_SECONDS: &'static str = "cache_ttl_seconds";
  pub const BATCH_SIZE: &'static str = "batch_size";
  pub const WORKER_THREADS: &'static str = "worker_threads";
  pub const ENABLE_METRICS: &'static str = "enable_metrics";

  /// Get all required configuration keys for validation
  pub fn get_required_keys() -> Vec<&'static str> {
    vec![
      Self::FRIENDSHIP_DB_URL,
      Self::FRIENDSHIP_REDIS_URL,
      Self::FRIENDSHIP_SHARDED_TIDB_CONFIGS,
    ]
  }

  /// Get all optional configuration keys with their defaults
  pub fn get_optional_keys_with_defaults() -> Vec<(&'static str, &'static str)> {
    vec![
      (Self::SERVER_HOST, "0.0.0.0"),
      (Self::SERVER_PORT, "8080"),
      (Self::MAX_CONNECTIONS, "1000"),
      (Self::REQUEST_TIMEOUT, "30"),
      (Self::SHUTDOWN_TIMEOUT, "10"),
      (Self::DB_MAX_CONNECTIONS, "50"),
      (Self::DB_MIN_CONNECTIONS, "5"),
      (Self::DB_CONNECTION_TIMEOUT, "30"),
      (Self::DB_IDLE_TIMEOUT, "600"),
      (Self::DB_MAX_LIFETIME, "1800"),
      (Self::REDIS_POOL_SIZE, "20"),
      (Self::REDIS_CONNECTION_TIMEOUT, "10"),
      (Self::REDIS_COMMAND_TIMEOUT, "5"),
      (Self::LOG_LEVEL, "info"),
      (Self::LOG_FORMAT, "json"),
      (Self::ENABLE_JSON_LOGGING, "true"),
      (Self::ENABLE_CONSOLE_LOGGING, "false"),
      (Self::ENABLE_AUTH, "true"),
      (Self::RATE_LIMIT_REQUESTS, "1000"),
      (Self::RATE_LIMIT_WINDOW_SECONDS, "60"),
      (Self::MAX_REQUEST_SIZE, "1048576"), // 1MB
      (Self::CACHE_TTL_SECONDS, "300"),
      (Self::BATCH_SIZE, "1000"),
      (Self::WORKER_THREADS, "0"), // 0 means auto-detect
      (Self::ENABLE_METRICS, "true"),
    ]
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_apollo_keys_completeness() {
    let required_keys = ApolloKeys::get_required_keys();
    assert!(!required_keys.is_empty());
    assert!(required_keys.contains(&ApolloKeys::FRIENDSHIP_DB_URL));
    assert!(required_keys.contains(&ApolloKeys::FRIENDSHIP_REDIS_URL));

    let optional_keys = ApolloKeys::get_optional_keys_with_defaults();
    assert!(!optional_keys.is_empty());
    
    // Check that we have defaults for common keys
    let key_names: Vec<&str> = optional_keys.iter().map(|(key, _)| *key).collect();
    assert!(key_names.contains(&ApolloKeys::SERVER_PORT));
    assert!(key_names.contains(&ApolloKeys::LOG_LEVEL));
  }
}
