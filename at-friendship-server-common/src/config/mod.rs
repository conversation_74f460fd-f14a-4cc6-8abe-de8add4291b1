//! Unified configuration management
//!
//! This module provides centralized configuration management with
//! environment-specific settings and validation.

pub mod database_config;
pub mod redis_config;
pub mod server_config;

use {serde::{Deserialize,
             Serialize},
     std::{env,
           time::Duration},
     tracing::{info,
               warn}};

/// Application environment types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Environment {
  Development,
  Testing,
  Staging,
  Production,
}

impl Environment {
  /// Detect environment from environment variables
  pub fn detect() -> Self {
    match env::var("ENVIRONMENT").as_deref() {
      Ok("production") | Ok("prod") => Environment::Production,
      Ok("staging") | Ok("stage") => Environment::Staging,
      Ok("testing") | Ok("test") => Environment::Testing,
      Ok("development") | Ok("dev") => Environment::Development,
      _ => {
        warn!("Unknown or unset ENVIRONMENT variable, defaulting to Development");
        Environment::Development
      }
    }
  }

  /// Check if this is a production environment
  pub fn is_production(&self) -> bool {
    matches!(self, Environment::Production)
  }

  /// Check if this is a development environment
  pub fn is_development(&self) -> bool {
    matches!(self, Environment::Development)
  }

  /// Check if this is a testing environment
  pub fn is_testing(&self) -> bool {
    matches!(self, Environment::Testing)
  }
}

/// Main application configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
  pub environment: Environment,
  pub server: ServerConfig,
  pub database: DatabaseConfig,
  pub redis: RedisConfig,
  pub logging: LoggingConfig,
  pub security: SecurityConfig,
  pub performance: PerformanceConfig,
}

impl AppConfig {
  /// Load configuration from environment and defaults
  pub fn load() -> Result<Self, ConfigError> {
    let environment = Environment::detect();
    info!("Loading configuration for environment: {:?}", environment);

    Ok(Self {
      environment: environment.clone(),
      server: ServerConfig::load(&environment)?,
      database: DatabaseConfig::load(&environment)?,
      redis: RedisConfig::load(&environment)?,
      logging: LoggingConfig::load(&environment)?,
      security: SecurityConfig::load(&environment)?,
      performance: PerformanceConfig::load(&environment)?,
    })
  }

  /// Validate configuration
  pub fn validate(&self) -> Result<(), ConfigError> {
    self.server.validate()?;
    self.database.validate()?;
    self.redis.validate()?;
    self.logging.validate()?;
    self.security.validate()?;
    self.performance.validate()?;
    Ok(())
  }
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
  pub host: String,
  pub port: u16,
  pub max_connections: u32,
  pub request_timeout: Duration,
  pub shutdown_timeout: Duration,
}

impl ServerConfig {
  pub fn load(environment: &Environment) -> Result<Self, ConfigError> {
    let config = match environment {
      Environment::Production => Self {
        host: env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string()),
        port: env::var("SERVER_PORT")
          .unwrap_or_else(|_| "8080".to_string())
          .parse()
          .map_err(|_| ConfigError::InvalidValue("SERVER_PORT".to_string()))?,
        max_connections: 1000,
        request_timeout: Duration::from_secs(30),
        shutdown_timeout: Duration::from_secs(10),
      },
      Environment::Staging => Self {
        host: env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string()),
        port: env::var("SERVER_PORT")
          .unwrap_or_else(|_| "8080".to_string())
          .parse()
          .map_err(|_| ConfigError::InvalidValue("SERVER_PORT".to_string()))?,
        max_connections: 500,
        request_timeout: Duration::from_secs(30),
        shutdown_timeout: Duration::from_secs(10),
      },
      Environment::Development => Self {
        host: env::var("SERVER_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
        port: env::var("SERVER_PORT")
          .unwrap_or_else(|_| "8080".to_string())
          .parse()
          .map_err(|_| ConfigError::InvalidValue("SERVER_PORT".to_string()))?,
        max_connections: 100,
        request_timeout: Duration::from_secs(60),
        shutdown_timeout: Duration::from_secs(5),
      },
      Environment::Testing => Self {
        host: "127.0.0.1".to_string(),
        port: 0, // Use random port for testing
        max_connections: 10,
        request_timeout: Duration::from_secs(10),
        shutdown_timeout: Duration::from_secs(1),
      },
    };

    Ok(config)
  }

  pub fn validate(&self) -> Result<(), ConfigError> {
    if self.port > 65535 {
      return Err(ConfigError::InvalidValue("port must be <= 65535".to_string()));
    }
    if self.max_connections == 0 {
      return Err(ConfigError::InvalidValue("max_connections must be > 0".to_string()));
    }
    Ok(())
  }

  pub fn server_address(&self) -> String {
    format!("{}:{}", self.host, self.port)
  }
}

/// Database configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
  pub url: String,
  pub max_connections: u32,
  pub min_connections: u32,
  pub connection_timeout: Duration,
  pub idle_timeout: Duration,
  pub max_lifetime: Duration,
}

impl DatabaseConfig {
  pub fn load(environment: &Environment) -> Result<Self, ConfigError> {
    let url = env::var("DATABASE_URL").map_err(|_| ConfigError::MissingRequired("DATABASE_URL".to_string()))?;

    let config = match environment {
      Environment::Production => Self {
        url,
        max_connections: 50,
        min_connections: 5,
        connection_timeout: Duration::from_secs(30),
        idle_timeout: Duration::from_secs(600),
        max_lifetime: Duration::from_secs(1800),
      },
      Environment::Staging => Self {
        url,
        max_connections: 25,
        min_connections: 3,
        connection_timeout: Duration::from_secs(20),
        idle_timeout: Duration::from_secs(300),
        max_lifetime: Duration::from_secs(1200),
      },
      Environment::Development => Self {
        url,
        max_connections: 10,
        min_connections: 1,
        connection_timeout: Duration::from_secs(10),
        idle_timeout: Duration::from_secs(300),
        max_lifetime: Duration::from_secs(3600),
      },
      Environment::Testing => Self {
        url,
        max_connections: 5,
        min_connections: 1,
        connection_timeout: Duration::from_secs(5),
        idle_timeout: Duration::from_secs(60),
        max_lifetime: Duration::from_secs(300),
      },
    };

    Ok(config)
  }

  pub fn validate(&self) -> Result<(), ConfigError> {
    if self.url.is_empty() {
      return Err(ConfigError::InvalidValue("database URL cannot be empty".to_string()));
    }
    if self.max_connections == 0 {
      return Err(ConfigError::InvalidValue("max_connections must be > 0".to_string()));
    }
    if self.min_connections > self.max_connections {
      return Err(ConfigError::InvalidValue("min_connections cannot exceed max_connections".to_string()));
    }
    Ok(())
  }
}

/// Redis configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisConfig {
  pub url: String,
  pub pool_size: u32,
  pub connection_timeout: Duration,
  pub command_timeout: Duration,
}

impl RedisConfig {
  pub fn load(environment: &Environment) -> Result<Self, ConfigError> {
    let url = env::var("REDIS_URL").map_err(|_| ConfigError::MissingRequired("REDIS_URL".to_string()))?;

    let config = match environment {
      Environment::Production => Self {
        url,
        pool_size: 20,
        connection_timeout: Duration::from_secs(10),
        command_timeout: Duration::from_secs(5),
      },
      Environment::Staging => Self {
        url,
        pool_size: 10,
        connection_timeout: Duration::from_secs(10),
        command_timeout: Duration::from_secs(5),
      },
      Environment::Development => Self {
        url,
        pool_size: 5,
        connection_timeout: Duration::from_secs(5),
        command_timeout: Duration::from_secs(10),
      },
      Environment::Testing => Self {
        url,
        pool_size: 2,
        connection_timeout: Duration::from_secs(2),
        command_timeout: Duration::from_secs(5),
      },
    };

    Ok(config)
  }

  pub fn validate(&self) -> Result<(), ConfigError> {
    if self.url.is_empty() {
      return Err(ConfigError::InvalidValue("Redis URL cannot be empty".to_string()));
    }
    if self.pool_size == 0 {
      return Err(ConfigError::InvalidValue("pool_size must be > 0".to_string()));
    }
    Ok(())
  }
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
  pub level: String,
  pub format: String,
  pub enable_json: bool,
  pub enable_console: bool,
}

impl LoggingConfig {
  pub fn load(environment: &Environment) -> Result<Self, ConfigError> {
    let config = match environment {
      Environment::Production => Self {
        level: env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
        format: "json".to_string(),
        enable_json: true,
        enable_console: false,
      },
      Environment::Staging => Self {
        level: env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
        format: "json".to_string(),
        enable_json: true,
        enable_console: true,
      },
      Environment::Development => Self {
        level: env::var("LOG_LEVEL").unwrap_or_else(|_| "debug".to_string()),
        format: "pretty".to_string(),
        enable_json: false,
        enable_console: true,
      },
      Environment::Testing => Self {
        level: env::var("LOG_LEVEL").unwrap_or_else(|_| "warn".to_string()),
        format: "compact".to_string(),
        enable_json: false,
        enable_console: true,
      },
    };

    Ok(config)
  }

  pub fn validate(&self) -> Result<(), ConfigError> {
    match self.level.as_str() {
      "trace" | "debug" | "info" | "warn" | "error" => Ok(()),
      _ => Err(ConfigError::InvalidValue("invalid log level".to_string())),
    }
  }
}

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
  pub enable_auth: bool,
  pub jwt_secret: Option<String>,
  pub rate_limit_requests: u32,
  pub rate_limit_window: Duration,
  pub max_request_size: usize,
}

impl SecurityConfig {
  pub fn load(environment: &Environment) -> Result<Self, ConfigError> {
    let config = match environment {
      Environment::Production => Self {
        enable_auth: true,
        jwt_secret: env::var("JWT_SECRET").ok(),
        rate_limit_requests: 1000,
        rate_limit_window: Duration::from_secs(60),
        max_request_size: 1024 * 1024, // 1MB
      },
      Environment::Staging => Self {
        enable_auth: true,
        jwt_secret: env::var("JWT_SECRET").ok(),
        rate_limit_requests: 500,
        rate_limit_window: Duration::from_secs(60),
        max_request_size: 1024 * 1024, // 1MB
      },
      Environment::Development => Self {
        enable_auth: false,
        jwt_secret: None,
        rate_limit_requests: 100,
        rate_limit_window: Duration::from_secs(60),
        max_request_size: 10 * 1024 * 1024, // 10MB
      },
      Environment::Testing => Self {
        enable_auth: false,
        jwt_secret: None,
        rate_limit_requests: 1000,
        rate_limit_window: Duration::from_secs(1),
        max_request_size: 1024 * 1024, // 1MB
      },
    };

    Ok(config)
  }

  pub fn validate(&self) -> Result<(), ConfigError> {
    if self.enable_auth && self.jwt_secret.is_none() {
      return Err(ConfigError::MissingRequired("JWT_SECRET required when auth is enabled".to_string()));
    }
    if self.rate_limit_requests == 0 {
      return Err(ConfigError::InvalidValue("rate_limit_requests must be > 0".to_string()));
    }
    if self.max_request_size == 0 {
      return Err(ConfigError::InvalidValue("max_request_size must be > 0".to_string()));
    }
    Ok(())
  }
}

/// Performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
  pub cache_ttl: Duration,
  pub batch_size: usize,
  pub worker_threads: usize,
  pub enable_metrics: bool,
}

impl PerformanceConfig {
  pub fn load(environment: &Environment) -> Result<Self, ConfigError> {
    let config = match environment {
      Environment::Production => Self {
        cache_ttl: Duration::from_secs(300), // 5 minutes
        batch_size: 1000,
        worker_threads: num_cpus::get(),
        enable_metrics: true,
      },
      Environment::Staging => Self {
        cache_ttl: Duration::from_secs(300), // 5 minutes
        batch_size: 500,
        worker_threads: num_cpus::get() / 2,
        enable_metrics: true,
      },
      Environment::Development => Self {
        cache_ttl: Duration::from_secs(60), // 1 minute
        batch_size: 100,
        worker_threads: 2,
        enable_metrics: false,
      },
      Environment::Testing => Self {
        cache_ttl: Duration::from_secs(10), // 10 seconds
        batch_size: 10,
        worker_threads: 1,
        enable_metrics: false,
      },
    };

    Ok(config)
  }

  pub fn validate(&self) -> Result<(), ConfigError> {
    if self.batch_size == 0 {
      return Err(ConfigError::InvalidValue("batch_size must be > 0".to_string()));
    }
    if self.worker_threads == 0 {
      return Err(ConfigError::InvalidValue("worker_threads must be > 0".to_string()));
    }
    Ok(())
  }
}

/// Configuration errors
#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
  #[error("Missing required configuration: {0}")]
  MissingRequired(String),

  #[error("Invalid configuration value: {0}")]
  InvalidValue(String),

  #[error("Configuration parsing error: {0}")]
  ParseError(String),
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_environment_detection() {
    // Test default case
    let env = Environment::detect();
    assert!(matches!(env, Environment::Development));
  }

  #[test]
  fn test_environment_methods() {
    assert!(Environment::Production.is_production());
    assert!(!Environment::Development.is_production());
    assert!(Environment::Development.is_development());
    assert!(Environment::Testing.is_testing());
  }

  #[test]
  fn test_server_config_validation() {
    let mut config = ServerConfig {
      host: "localhost".to_string(),
      port: 8080,
      max_connections: 100,
      request_timeout: Duration::from_secs(30),
      shutdown_timeout: Duration::from_secs(10),
    };

    assert!(config.validate().is_ok());

    config.port = 70000; // Invalid port
    assert!(config.validate().is_err());

    config.port = 8080;
    config.max_connections = 0; // Invalid max_connections
    assert!(config.validate().is_err());
  }

  #[test]
  fn test_server_address() {
    let config = ServerConfig {
      host: "localhost".to_string(),
      port: 8080,
      max_connections: 100,
      request_timeout: Duration::from_secs(30),
      shutdown_timeout: Duration::from_secs(10),
    };

    assert_eq!(config.server_address(), "localhost:8080");
  }
}
