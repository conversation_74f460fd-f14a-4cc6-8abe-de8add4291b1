use {at_friendship_domain_model::all::*,
     prost::Message};

// Combined converter trait for handling both request conversion and response creation
pub trait ProtoConverter<Req, ReqDomain, RespDomain, Resp>
where
  Req: Message + Default,
  Resp: Message + Default, {
  // Request conversion methods
  fn decode_and_convert(bytes: &[u8]) -> FriendshipResult<ReqDomain>
  where
    Self: Sized, {
    let request = Req::decode(bytes).map_err(|e| FriendshipError::InvalidInput(format!("Invalid request: {e}")))?;
    Self::convert_request(request)
  }

  fn convert_request(request: Req) -> FriendshipResult<ReqDomain>;

  // Response creation methods
  fn create_response(domain_result: RespDomain) -> Resp;
}

// Macro for simplifying ProtoConverter trait implementation
#[macro_export]
macro_rules! impl_proto_converter {
  (
    $request_type:ty, $request_result:ty, $response_result:ty, $response_type:ty,
    convert_request = |$req:ident| $convert_request_body:expr,
    create_response = |$resp:ident| $create_response_body:expr
  ) => {
    impl ProtoConverter<$request_type, $request_result, $response_result, $response_type> for FriendshipProtoConverter {
      fn convert_request($req: $request_type) -> FriendshipResult<$request_result> {
        $convert_request_body
      }

      fn create_response($resp: $response_result) -> $response_type {
        $create_response_body
      }
    }
  };
}

// Macro for simplifying public API methods in FriendshipProtoConverter
#[macro_export]
macro_rules! define_converter_method {
  // Combined request and response methods
  (
    converter $name:ident: $req:ty, $domain:ty, $resp_domain:ty, $resp:ty,
    request_method = $req_method:ident,
    response_method = $resp_method:ident
  ) => {
    pub fn $req_method(bytes: &[u8]) -> FriendshipResult<$domain> {
      <Self as ProtoConverter<$req, $domain, $resp_domain, $resp>>::decode_and_convert(bytes)
    }

    pub fn $resp_method(result: $resp_domain) -> $resp {
      <Self as ProtoConverter<$req, $domain, $resp_domain, $resp>>::create_response(result)
    }
  };
}

pub mod friendship;
pub mod friendship_mq;
