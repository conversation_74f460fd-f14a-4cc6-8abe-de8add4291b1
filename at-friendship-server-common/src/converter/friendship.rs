use {crate::converter::ProtoConverter,
     at_friendship_domain_model::all::*,
     at_friendship_protocol_pb::prelude::*,
     at_hermes::prelude::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::{id::uuid::UuidBytes,
                          value::time::{CreateAt,
                                        UpdateAt}},
     prost::Message};

// Import macros
use crate::{define_converter_method,
            impl_proto_converter};

pub struct FriendshipProtoConverter;

// Utility methods
impl FriendshipProtoConverter {
  fn uuid_bytes_from_slice(user_id_bytes: &[u8]) -> FriendshipResult<UuidBytes> {
    <&[u8] as TryInto<[u8; 16]>>::try_into(user_id_bytes)
      .map(UuidBytes::new)
      .map_err(|_| FriendshipError::InvalidInput("Invalid user ID".to_string()))
  }

  pub fn extract_id_from_bytes<T>(user_id_bytes: &[u8], make_id: impl Fn(UuidBytes) -> T) -> FriendshipResult<T> {
    Self::uuid_bytes_from_slice(user_id_bytes).map(make_id)
  }

  pub fn encode_to_amp<T: Message>(message: T) -> AMP {
    AMP::new(message.encode_to_vec())
  }

  fn contact_to_contact_info(contact: &Contact) -> ContactInfo {
    ContactInfo {
      phone_number: PhoneNumber::new(contact.phone_number),
      name:         contact.name.as_ref().map(|n| ContactName::new(n.clone())),
    }
  }
}

// Contact related conversions
impl_proto_converter!(
  SyncContactsRequest,
  ContactOperation,
  bool,
  SyncContactsResponse,
  convert_request = |request| {
    let user_id = Self::extract_id_from_bytes(&request.owner_auid, |bytes| ContactOwnerId::new(Auid::new(bytes)))?;
    request
      .operation
      .map(|operation| ContactOperation {
        owner_id: user_id,
        add:      operation.add.iter().map(Self::contact_to_contact_info).collect(),
        delete:   operation.delete.iter().map(Self::contact_to_contact_info).collect(),
        update:   operation.update.iter().map(Self::contact_to_contact_info).collect(),
      })
      .ok_or_else(|| FriendshipError::InvalidInput("Missing operation in SyncContactsRequest".to_string()))
  },
  create_response = |success| SyncContactsResponse { success }
);

// Botim user related conversions
impl_proto_converter!(
  GetOwnedBotimUsersRequest,
  (ContactOwnerId, Vec<PhoneNumber>, Option<String>),
  (Vec<BotimUserEntity>, bool),
  GetOwnedBotimUsersResponse,
  convert_request = |request| {
    let owner_id = Self::extract_id_from_bytes(&request.owner_auid, |bytes| ContactOwnerId::new(Auid::new(bytes)))?;
    Ok((
      owner_id,
      request
        .phone_number_filters
        .iter()
        .map(|s| PhoneNumber::new(*s))
        .collect(),
      request.task_id,
    ))
  },
  create_response = |result| {
    let (entities, status) = result;
    GetOwnedBotimUsersResponse {
      botim_users: entities
        .iter()
        .map(|entity| at_friendship_protocol_pb::prelude::BotimUser {
          auid:         entity.botim_user_id.inner_ref().inner_ref().inner_ref().to_vec(),
          auid_repr:    entity.auid_repr.inner_ref().to_string(),
          phone_number: *entity.phone_number.inner_ref(),
        })
        .collect(),
      task_status: Some(status),
    }
  }
);

// Save Botim user related conversions
impl_proto_converter!(
  SaveBotimUserRequest,
  Vec<BotimUserEntity>,
  bool,
  SaveBotimUserResponse,
  convert_request = |request| {
    request
      .botim_users
      .iter()
      .map(|botim_user| -> FriendshipResult<BotimUserEntity> {
        let botim_user_id = Self::extract_id_from_bytes(&botim_user.auid, |bytes| BotimUserId::new(Auid::new(bytes)))?;
        Ok(BotimUserEntity {
          botim_user_id,
          auid_repr: AuidRepr::new(botim_user.auid_repr.clone()),
          phone_number: PhoneNumber::new(botim_user.phone_number),
          created_at: CreateAt::now(),
          updated_at: UpdateAt::now(),
        })
      })
      .collect()
  },
  create_response = |success| SaveBotimUserResponse { success }
);

// Block user related conversions
impl_proto_converter!(
  SaveBlockedUserRequest,
  (BotimUserId, BotimUserId, BlockUserStatus),
  bool,
  SaveBlockedUserResponse,
  convert_request = |request| {
    let user_id = Self::extract_id_from_bytes(&request.user_id, |bytes| BotimUserId::new(Auid::new(bytes)))?;
    let blocked_user_id =
      Self::extract_id_from_bytes(&request.blocked_user_id, |bytes| BotimUserId::new(Auid::new(bytes)))?;
    let status = BlockUserStatus::from(request.status as u8);
    Ok((user_id, blocked_user_id, status))
  },
  create_response = |success| SaveBlockedUserResponse { success }
);

impl_proto_converter!(
  GetBlockedUsersRequest,
  BotimUserId,
  Vec<BlockedUserEntity>,
  GetBlockedUsersResponse,
  convert_request = |request| {
    let user_id = Self::extract_id_from_bytes(&request.user_id, |bytes| BotimUserId::new(Auid::new(bytes)))?;
    Ok(user_id)
  },
  create_response = |entities| {
    GetBlockedUsersResponse {
      blocked_users: entities
        .iter()
        .map(|entity| entity.user_id.inner_ref().inner_ref().inner_ref().to_vec())
        .collect(),
    }
  }
);

// Check if blocked related conversions
impl_proto_converter!(
  IsBlockedRequest,
  (BotimUserId, BotimUserId),
  bool,
  IsBlockedResponse,
  convert_request = |request| {
    let user_id = Self::extract_id_from_bytes(&request.user_id, |bytes| BotimUserId::new(Auid::new(bytes)))?;
    let blocked_user_id =
      Self::extract_id_from_bytes(&request.blocked_user_id, |bytes| BotimUserId::new(Auid::new(bytes)))?;
    Ok((user_id, blocked_user_id))
  },
  create_response = |is_blocked| IsBlockedResponse { is_blocked }
);

// Get existing contact phone numbers related conversions
impl_proto_converter!(
  GetExistingContactPhoneNumbersRequest,
  (ContactOwnerId, Vec<PhoneNumber>),
  (Vec<PhoneNumber>, bool),
  GetExistingContactPhoneNumbersResponse,
  convert_request = |request| {
    let owner_id = Self::extract_id_from_bytes(&request.owner_auid, |bytes| ContactOwnerId::new(Auid::new(bytes)))?;
    Ok((
      owner_id,
      request
        .phone_number_filters
        .iter()
        .map(|s| PhoneNumber::new(*s))
        .collect(),
    ))
  },
  create_response = |result| {
    let (phone_numbers, success) = result;
    GetExistingContactPhoneNumbersResponse {
      success,
      phone_numbers: phone_numbers.iter().map(|s| *s.inner_ref()).collect(),
    }
  }
);

// Get existing contact botim user ids related conversions
impl_proto_converter!(
  GetExistingContactOwnersRequest,
  (BotimUserId, Vec<ContactOwnerId>),
  (Vec<ContactOwnerId>, bool),
  GetExistingContactOwnersResponse,
  convert_request = |request| {
    let botim_user_id =
      Self::extract_id_from_bytes(&request.botim_user_id, |bytes| BotimUserId::new(Auid::new(bytes)))?;
    Ok((
      botim_user_id,
      request
        .owner_auids
        .iter()
        .map(|s| Self::extract_id_from_bytes(s, |bytes| ContactOwnerId::new(Auid::new(bytes))))
        .collect::<FriendshipResult<Vec<ContactOwnerId>>>()?,
    ))
  },
  create_response = |result| {
    let (owner_ids, success) = result;
    GetExistingContactOwnersResponse {
      success,
      owner_auids: owner_ids
        .iter()
        .map(|s| s.inner_ref().inner_ref().inner_ref().to_vec())
        .collect(),
    }
  }
);

impl_proto_converter!(
  IsBlockedByUsersRequest,
  (Vec<BotimUserId>, BotimUserId),
  (Vec<BotimUserId>, bool),
  IsBlockedByUsersResponse,
  convert_request = |request| {
    let blocked_user_id =
      Self::extract_id_from_bytes(&request.blocked_user_id, |bytes| BotimUserId::new(Auid::new(bytes)))?;
    Ok((
      request
        .user_ids
        .iter()
        .map(|s| Self::extract_id_from_bytes(s, |bytes| BotimUserId::new(Auid::new(bytes))))
        .collect::<FriendshipResult<Vec<BotimUserId>>>()?,
      blocked_user_id,
    ))
  },
  create_response = |result| {
    let (blocked_user_ids, success) = result;
    IsBlockedByUsersResponse {
      success,
      blocked_user_ids: blocked_user_ids
        .iter()
        .map(|s| s.inner_ref().inner_ref().inner_ref().to_vec())
        .collect(),
    }
  }
);

// Public API methods
impl FriendshipProtoConverter {
  // Combined API methods using converter macro
  define_converter_method!(
    converter sync_contacts: SyncContactsRequest, ContactOperation, bool, SyncContactsResponse,
    request_method = convert_sync_contacts_request,
    response_method = create_sync_contacts_response
  );

  define_converter_method!(
    converter get_owned_botim_users: GetOwnedBotimUsersRequest, (ContactOwnerId, Vec<PhoneNumber>, Option<String>), (Vec<BotimUserEntity>, bool), GetOwnedBotimUsersResponse,
    request_method = convert_get_owned_botim_users_request,
    response_method = create_get_owned_botim_users_response
  );

  define_converter_method!(
    converter save_blocked_user: SaveBlockedUserRequest, (BotimUserId, BotimUserId, BlockUserStatus), bool, SaveBlockedUserResponse,
    request_method = convert_save_blocked_user_request,
    response_method = create_save_blocked_user_response
  );

  define_converter_method!(
    converter get_blocked_users: GetBlockedUsersRequest, BotimUserId, Vec<BlockedUserEntity>, GetBlockedUsersResponse,
    request_method = convert_get_blocked_users_request,
    response_method = create_get_blocked_users_response
  );

  define_converter_method!(
    converter save_botim_user: SaveBotimUserRequest, Vec<BotimUserEntity>, bool, SaveBotimUserResponse,
    request_method = convert_save_botim_user_request,
    response_method = create_save_botim_user_response
  );

  define_converter_method!(
    converter is_blocked: IsBlockedRequest, (BotimUserId, BotimUserId), bool, IsBlockedResponse,
    request_method = convert_is_blocked_request,
    response_method = create_is_blocked_response
  );

  define_converter_method!(
    converter get_existing_contact_phone_numbers: GetExistingContactPhoneNumbersRequest, (ContactOwnerId, Vec<PhoneNumber>), (Vec<PhoneNumber>, bool), GetExistingContactPhoneNumbersResponse,
    request_method = convert_get_existing_contact_phone_numbers_request,
    response_method = create_get_existing_contact_phone_numbers_response
  );

  define_converter_method!(
    converter get_existing_contact_owners: GetExistingContactOwnersRequest, (BotimUserId, Vec<ContactOwnerId>), (Vec<ContactOwnerId>, bool), GetExistingContactOwnersResponse,
    request_method = convert_get_existing_contact_owners_request,
    response_method = create_get_existing_contact_owners_response
  );

  define_converter_method!(
    converter is_blocked_by_users: IsBlockedByUsersRequest, (Vec<BotimUserId>, BotimUserId), (Vec<BotimUserId>, bool), IsBlockedByUsersResponse,
    request_method = convert_is_blocked_by_users_request,
    response_method = create_is_blocked_by_users_response
  );
}
