use {at_deps::at_friendship::*,
     at_friendship_domain_model::errors::{FriendshipError,
                                          FriendshipResult},
     serde::{Deserialize,
             Serialize}};
/// The main event type that will be serialized and sent to the message broker
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Event {
  pub metadata: EventMetadata,
  pub payload:  EventPayload,
}

/// Metadata for the event message
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EventMetadata {
  pub event_id:   String,
  pub event_type: String,
  pub source:     String,
  pub timestamp:  i64,
  pub version:    String,
}

/// Generic payload for the event
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EventPayload {
  #[serde(flatten)]
  pub data: serde_json::Value,
}

/// Event payload for serialization
///
/// This structure represents the data that will be serialized and sent
/// to RabbitMQ for authorization events.
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthorizationEventPayload {
  /// User AUID (Algento Unique Identifier)
  pub auid:                String,
  /// Representation of the AUID
  pub repr:                String,
  /// Credential identity
  /// - "mobile:************"
  /// - "username:john.doe"
  /// - "email:<EMAIL>"
  pub credential_identity: String,
  /// Device ID that initiated the authorization
  pub device_id:           String,
  /// Type of credential used for authentication (e.g., "MobileNumberOtp")
  pub credential_type:     String,
  /// Region code of the user
  pub region_code:         String,
  /// Registration source identifier
  /// - "0": Botim Android
  /// - "1": Botim iOS
  /// - "2": Botim Web
  pub reg_src:             String,
  /// Event timestamp in milliseconds since epoch
  pub timestamp:           i64,
}

impl AuthorizationEventPayload {
  pub fn parse_from_json(json: &str) -> FriendshipResult<Self> {
    let event = Event::parse_from_json(json)?;
    let payload = event.payload.data;
    serde_json::from_value(payload)
      .map_err(|e| FriendshipError::MqDeserialization(format!("Failed to parse Authorization JSON from mq: {e}")))
  }
}

impl Event {
  pub fn parse_from_json(json: &str) -> FriendshipResult<Self> {
    serde_json::from_str(json)
      .map_err(|e| FriendshipError::MqDeserialization(format!("Failed to parse Event JSON from mq: {e}")))
  }
}
