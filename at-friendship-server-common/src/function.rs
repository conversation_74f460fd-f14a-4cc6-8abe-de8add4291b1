use {at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_friendship_integration::prelude::*,
     at_friendship_stub::prelude::*,
     at_hermes::prelude::*,
     at_internal_deps::at_friendship::*,
     at_janus_client::prelude::*,
     fred::clients::Pool as RedisPool,
     sqlx::{MySql,
            Pool},
     std::collections::HashMap,
     tracing::{error,
               info}};

use {crate::handler::{friendship::FriendshipHandler,
                      friendship_mq::FriendshipMqHandler},
     futures::{FutureExt,
               future::BoxFuture}};

// Handler function type
type HandlerFunction =
  for<'a> fn(&'a ServerFunction, &'a ApplicationMessage) -> BoxFuture<'a, FriendshipResult<ApplicationMessage>>;

/// Server function dispatcher with handler registry
#[derive(Clone)]
pub struct ServerFunction {
  friendship_handler:    <PERSON>Handler,
  handlers:              <PERSON>h<PERSON>ap<String, HandlerFunction>,
  friendship_mq_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
}

impl ServerFunction {
  pub async fn new(
    mysql_pool: Pool<MySql>,
    tidb_pools: Vec<Pool<MySql>>,
    janus_client: JanusClient<EtcdServiceFetcher>,
    redis_pool: RedisPool,
  ) -> Self {
    let friendship_app = FriendshipAdapter::new(mysql_pool, tidb_pools, janus_client, redis_pool);
    let friendship_handler = FriendshipHandler::new(friendship_app.clone());
    let friendship_mq_handler = FriendshipMqHandler::new(friendship_app);

    let mut server_function = Self {
      friendship_handler,
      handlers: HashMap::new(),
      friendship_mq_handler,
    };

    server_function.register_handlers();
    server_function
  }

  fn register_handlers(&mut self) {
    // Register all handlers in a single place for easier maintenance
    let regular_handlers: [(String, HandlerFunction); 9] = [
      (ADM_FRIENDSHIP_SYNC_CONTACTS.to_string(), |s, req| {
        s.friendship_handler.handle_sync_contacts(req).boxed()
      }),
      (ADM_FRIENDSHIP_GET_OWNED_BOTIM_USERS.to_string(), |s, req| {
        s.friendship_handler.handle_get_owned_botim_users(req).boxed()
      }),
      (ADM_FRIENDSHIP_SAVE_BLOCKED_USER.to_string(), |s, req| {
        s.friendship_handler.handle_save_blocked_user(req).boxed()
      }),
      (ADM_FRIENDSHIP_GET_BLOCKED_USERS.to_string(), |s, req| {
        s.friendship_handler.handle_get_blocked_users(req).boxed()
      }),
      (ADM_FRIENDSHIP_SAVE_BOTIM_USER.to_string(), |s, req| {
        s.friendship_handler.handle_save_botim_user(req).boxed()
      }),
      (ADM_FRIENDSHIP_IS_BLOCKED.to_string(), |s, req| {
        s.friendship_handler.handle_is_blocked(req).boxed()
      }),
      (
        ADM_FRIENDSHIP_GET_EXISTING_CONTACT_PHONE_NUMBERS.to_string(),
        |s, req| {
          s.friendship_handler
            .handle_get_existing_contact_phone_numbers(req)
            .boxed()
        },
      ),
      (ADM_FRIENDSHIP_GET_EXISTING_CONTACT_OWNERS.to_string(), |s, req| {
        s.friendship_handler.handle_get_existing_contact_owners(req).boxed()
      }),
      (ADM_FRIENDSHIP_IS_BLOCKED_BY_USERS.to_string(), |s, req| {
        s.friendship_handler.handle_is_blocked_by_users(req).boxed()
      }),
    ];

    // Register regular handlers
    for (command, handler) in regular_handlers {
      self.register_handler(command, handler);
    }
    info!("Registered {} command handlers", self.handlers.len());
  }

  fn register_handler(&mut self, command: String, handler: HandlerFunction) {
    self.handlers.insert(command, handler);
  }

  #[tracing::instrument(name = "dispatch_server_function", skip(self, request))]
  pub async fn dispatch_server_function(&self, request: &ApplicationMessage) -> FriendshipResult<ApplicationMessage> {
    let adm = request
      .meta_adm()
      .map(|x| x.inner_ref().as_str())
      .ok_or(FriendshipError::InvalidInput("adm is not set".to_string()))?;

    match self.handlers.get(adm) {
      Some(handler) => handler(self, request).await,
      None => {
        let err = FriendshipError::InvalidInput(format!("Unknown command: {adm}"));
        error!("Command handler not found: {}", err);
        Err(err)
      }
    }
  }

  #[tracing::instrument(name = "dispatch_mq_server_function", skip(self, request))]
  pub async fn dispatch_mq_server_function(&self, request: &str) -> FriendshipResult<()> {
    info!("dispatch_mq_server_function: {}", request);
    self
      .friendship_mq_handler
      .new_botim_user_notification(request)
      .await
      .inspect(|_| {
        info!("Success dispatching mq server function: {}", request);
      })
      .inspect_err(|e| {
        error!("Error dispatching mq server function: {}", e);
      })
  }
}
