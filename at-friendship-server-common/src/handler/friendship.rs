use {crate::converter::friendship::FriendshipProtoConverter,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_friendship_integration::prelude::*,
     at_hermes::prelude::*,
     at_internal_deps::at_friendship::*,
     futures::{FutureExt,
               future::BoxFuture},
     std::{sync::Arc,
           time::Instant},
     tracing::{error,
               info}};

#[derive(Clone)]
pub struct FriendshipHandler {
  friendship_adapter: Arc<FriendshipAdapter>,
}

impl FriendshipHandler {
  pub fn new(friendship_adapter: FriendshipAdapter) -> Self {
    Self {
      friendship_adapter: Arc::new(friendship_adapter),
    }
  }

  // Simplified generic handler method
  async fn handle_generic<'a, D, R, P>(
    &'a self,
    request: &'a ApplicationMessage,
    decode_fn: impl FnOnce(&[u8]) -> FriendshipResult<D>,
    domain_logic: impl FnOnce(D) -> BoxFuture<'a, FriendshipResult<R>> + 'a,
    response_creator: impl FnOnce(R) -> P + 'a,
    info_label: &str,
  ) -> FriendshipResult<ApplicationMessage>
  where
    P: prost::Message, {
    let start = Instant::now();

    // 1. Parse request
    let req_bytes = request
      .payload
      .as_ref()
      .ok_or(FriendshipError::InvalidInput("payload is not set".to_string()))?
      .inner_ref()
      .as_slice();
    let domain_input = decode_fn(req_bytes)?;

    // 2. Execute domain logic
    let domain_result = domain_logic(domain_input).await?;

    // Create response and record time
    let resp_amp = FriendshipProtoConverter::encode_to_amp(response_creator(domain_result));
    info!("{} [total time: {:?}]", info_label, start.elapsed());

    Ok(request.update_payload(resp_amp))
  }

  #[tracing::instrument(name = "handle_sync_contacts", skip(self, request))]
  pub async fn handle_sync_contacts(&self, request: &ApplicationMessage) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_sync_contacts_request,
        |contact_operations| {
          let app = Arc::clone(&self.friendship_adapter);
          async move {
            app.sync_contacts(contact_operations).await.map(|_| true).map_err(|err| {
              error!("Sync contacts operation failed: {:?}", err);
              err // Return the actual error instead of swallowing it
            })
          }
          .boxed()
        },
        FriendshipProtoConverter::create_sync_contacts_response,
        "Sync contacts",
      )
      .await
  }

  #[tracing::instrument(name = "handle_get_owned_botim_users", skip(self, request))]
  pub async fn handle_get_owned_botim_users(
    &self,
    request: &ApplicationMessage,
  ) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_get_owned_botim_users_request,
        |request| {
          let app = Arc::clone(&self.friendship_adapter);
          let (owner_id, phone_number_filters, task_id) = request;
          async move {
            let (botim_user_entities, status) = app
              .get_owned_botim_users(&owner_id, &phone_number_filters, &task_id)
              .await?;
            Ok((botim_user_entities, status))
          }
          .boxed()
        },
        FriendshipProtoConverter::create_get_owned_botim_users_response,
        "Get owned botim users",
      )
      .await
  }

  #[tracing::instrument(name = "handle_save_blocked_user", skip(self, request))]
  pub async fn handle_save_blocked_user(&self, request: &ApplicationMessage) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_save_blocked_user_request,
        |(user_id, blocked_user_id, status)| {
          let app = Arc::clone(&self.friendship_adapter);
          async move {
            app.save_blocked_user(&user_id, &blocked_user_id, status).await.map(|_| true).map_err(|err| {
              error!("Save blocked user operation failed: {:?}", err);
              err // Return the actual error instead of swallowing it
            })
          }
          .boxed()
        },
        FriendshipProtoConverter::create_save_blocked_user_response,
        "Save blocked user",
      )
      .await
  }

  #[tracing::instrument(name = "handle_get_blocked_users", skip(self, request))]
  pub async fn handle_get_blocked_users(&self, request: &ApplicationMessage) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_get_blocked_users_request,
        |user_id| {
          let app = Arc::clone(&self.friendship_adapter);
          async move {
            let blocked_user_entities = app.get_blocked_users(&user_id).await.inspect_err(|err| {
              error!("Get blocked users operation failed: {:?}", err);
            })?;
            Ok(blocked_user_entities)
          }
          .boxed()
        },
        FriendshipProtoConverter::create_get_blocked_users_response,
        "Get blocked users",
      )
      .await
  }

  #[tracing::instrument(name = "handle_save_botim_user", skip(self, request))]
  pub async fn handle_save_botim_user(&self, request: &ApplicationMessage) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_save_botim_user_request,
        |botim_user_entities| {
          let app = Arc::clone(&self.friendship_adapter);
          async move {
            match app.save_botim_user(&botim_user_entities).await {
              Ok(_) => Ok(true),
              Err(err) => {
                error!("Save botim user operation failed: {:?}", err);
                Ok(false) // Still return success with false flag
              }
            }
          }
          .boxed()
        },
        FriendshipProtoConverter::create_save_botim_user_response,
        "Save botim user",
      )
      .await
  }

  #[tracing::instrument(name = "handle_is_blocked", skip(self, request))]
  pub async fn handle_is_blocked(&self, request: &ApplicationMessage) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_is_blocked_request,
        |(user_id, blocked_user_id)| {
          let app = Arc::clone(&self.friendship_adapter);
          async move {
            let is_blocked = app.is_blocked(&user_id, &blocked_user_id).await.inspect_err(|err| {
              error!("Is blocked operation failed: {:?}", err);
            })?;
            match is_blocked {
              true => Ok(true),
              false => Ok(false),
            }
          }
          .boxed()
        },
        FriendshipProtoConverter::create_is_blocked_response,
        "Is blocked",
      )
      .await
  }

  #[tracing::instrument(name = "handle_get_existing_contact_phone_numbers", skip(self, request))]
  pub async fn handle_get_existing_contact_phone_numbers(
    &self,
    request: &ApplicationMessage,
  ) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_get_existing_contact_phone_numbers_request,
        |request| {
          let app = Arc::clone(&self.friendship_adapter);
          let (owner_id, phone_number_filters) = request;
          async move {
            match app
              .get_existing_contact_phone_numbers(&owner_id, &phone_number_filters)
              .await
            {
              Ok(phone_numbers) => Ok((phone_numbers, true)),
              Err(err) => {
                error!("Get existing contact phone numbers operation failed: {:?}", err);
                Ok((vec![], false))
              }
            }
          }
          .boxed()
        },
        FriendshipProtoConverter::create_get_existing_contact_phone_numbers_response,
        "Get existing contact phone numbers",
      )
      .await
  }

  #[tracing::instrument(name = "handle_get_existing_contact_owners", skip(self, request))]
  pub async fn handle_get_existing_contact_owners(
    &self,
    request: &ApplicationMessage,
  ) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_get_existing_contact_owners_request,
        |request| {
          let app = Arc::clone(&self.friendship_adapter);
          let (botim_user_id, owner_ids) = request;
          async move {
            match app.get_existing_contact_owners(&botim_user_id, &owner_ids).await {
              Ok(owner_ids) => Ok((owner_ids, true)),
              Err(err) => {
                error!("Get existing contact owner ids operation failed: {:?}", err);
                Ok((vec![], false))
              }
            }
          }
          .boxed()
        },
        FriendshipProtoConverter::create_get_existing_contact_owners_response,
        "Get existing contact owner ids",
      )
      .await
  }

  #[tracing::instrument(name = "handle_is_blocked_by_users", skip(self, request))]
  pub async fn handle_is_blocked_by_users(&self, request: &ApplicationMessage) -> FriendshipResult<ApplicationMessage> {
    self
      .handle_generic(
        request,
        FriendshipProtoConverter::convert_is_blocked_by_users_request,
        |request| {
          let app = Arc::clone(&self.friendship_adapter);
          let (user_ids, blocked_user_id) = request;
          async move {
            match app.is_blocked_by_users(&user_ids, &blocked_user_id).await {
              Ok(blocked_user_ids) => Ok((blocked_user_ids, true)),
              Err(_) => Ok((vec![], false)),
            }
          }
          .boxed()
        },
        FriendshipProtoConverter::create_is_blocked_by_users_response,
        "Is blocked by users",
      )
      .await
  }
}
