use {crate::converter::friendship_mq::AuthorizationEventPayload,
     at_deps::at_friendship::*,
     at_friendship_domain_model::all::*,
     at_friendship_integration::prelude::*,
     at_internal_deps::at_friendship::*,
     at_lib_comm_domain::{id::uuid::UuidBytes,
                          value::time::{CreateAt,
                                        UpdateAt}},
     std::sync::Arc,
     tracing::{error,
               info}};

#[derive(Clone)]
pub struct FriendshipMqHandler {
  friendship_adapter: Arc<FriendshipAdapter>,
}

impl FriendshipMqHandler {
  pub fn new(friendship_adapter: FriendshipAdapter) -> Self {
    Self {
      friendship_adapter: Arc::new(friendship_adapter),
    }
  }

  #[tracing::instrument(name = "new_botim_user_notification", skip(self, payload_str))]
  pub async fn new_botim_user_notification(&self, payload_str: &str) -> FriendshipResult<()> {
    let payload_data = AuthorizationEventPayload::parse_from_json(payload_str)?;
    info!(
      "new_botim_user_notification: {:?} for phone number: {} from mq",
      payload_data, payload_data.credential_identity
    );

    let phone_number_u64 = payload_data
      .credential_identity
      .parse::<u64>()
      .map_err(|e| FriendshipError::InvalidInput(format!("Invalid phone number: {e}")))?;

    let botim_user_entity = BotimUserEntity {
      botim_user_id: BotimUserId::new(Auid::new(UuidBytes::generate())),
      auid_repr:     AuidRepr::new(payload_data.auid.to_string()),
      phone_number:  PhoneNumber::new(phone_number_u64),
      created_at:    CreateAt::now(),
      updated_at:    UpdateAt::now(),
    };
    let _ = self
      .friendship_adapter
      .new_botim_user_notification(&botim_user_entity, RegisterEvent::Register)
      .await
      .inspect_err(|e| {
        error!("Error dispatching mq server function: {}", e);
      })
      .inspect(|_| {
        info!("Success dispatching mq server function: {}", payload_str);
      });
    Ok(())
  }
}
