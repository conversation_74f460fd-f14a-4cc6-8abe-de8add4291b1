//! Health check implementations
//!
//! This module provides health check implementations for various
//! service components including databases, caches, and external services.

use {super::{HealthCheckResult,
             HealthStatus},
     fred::prelude::*,
     sqlx::{MySql,
            Pool},
     std::time::{Duration,
                 Instant},
     tracing::{debug,
               error}};

/// Database health checker
pub struct DatabaseHealthChecker;

impl DatabaseHealthChecker {
  /// Check MySQL database health
  pub async fn check_mysql_health(pool: &Pool<MySql>, component_name: &str) -> HealthCheckResult {
    let start = Instant::now();
    
    match Self::perform_mysql_health_check(pool).await {
      Ok(()) => {
        let response_time = start.elapsed();
        debug!("MySQL health check for {} passed in {:?}", component_name, response_time);
        
        if response_time > Duration::from_millis(1000) {
          HealthCheckResult::degraded(
            component_name.to_string(),
            format!("Slow response time: {:?}", response_time),
            response_time,
          )
        } else {
          HealthCheckResult::healthy(component_name.to_string(), response_time)
        }
      }
      Err(e) => {
        let response_time = start.elapsed();
        error!("MySQL health check for {} failed: {:?}", component_name, e);
        HealthCheckResult::unhealthy(
          component_name.to_string(),
          format!("Database connection failed: {}", e),
          response_time,
        )
      }
    }
  }

  /// Perform actual MySQL health check
  async fn perform_mysql_health_check(pool: &Pool<MySql>) -> Result<(), sqlx::Error> {
    let mut conn = pool.acquire().await?;
    sqlx::query("SELECT 1").fetch_one(&mut *conn).await?;
    Ok(())
  }

  /// Check multiple MySQL databases health
  pub async fn check_multiple_mysql_health(
    pools: &[Pool<MySql>],
    base_component_name: &str,
  ) -> Vec<HealthCheckResult> {
    let mut results = Vec::new();
    
    for (i, pool) in pools.iter().enumerate() {
      let component_name = format!("{}_shard_{}", base_component_name, i);
      let result = Self::check_mysql_health(pool, &component_name).await;
      results.push(result);
    }
    
    results
  }
}

/// Redis health checker
pub struct RedisHealthChecker;

impl RedisHealthChecker {
  /// Check Redis health
  pub async fn check_redis_health(pool: &fred::clients::Pool, component_name: &str) -> HealthCheckResult {
    let start = Instant::now();
    
    match Self::perform_redis_health_check(pool).await {
      Ok(()) => {
        let response_time = start.elapsed();
        debug!("Redis health check for {} passed in {:?}", component_name, response_time);
        
        if response_time > Duration::from_millis(500) {
          HealthCheckResult::degraded(
            component_name.to_string(),
            format!("Slow response time: {:?}", response_time),
            response_time,
          )
        } else {
          HealthCheckResult::healthy(component_name.to_string(), response_time)
        }
      }
      Err(e) => {
        let response_time = start.elapsed();
        error!("Redis health check for {} failed: {:?}", component_name, e);
        HealthCheckResult::unhealthy(
          component_name.to_string(),
          format!("Redis connection failed: {}", e),
          response_time,
        )
      }
    }
  }

  /// Perform actual Redis health check
  async fn perform_redis_health_check(pool: &fred::clients::Pool) -> Result<(), fred::error::RedisError> {
    let test_key = "health_check_key";
    let test_value = "health_check_value";
    
    // Test SET and GET operations
    pool.set::<(), _, _>(test_key, test_value, None, None, false).await?;
    let retrieved_value: String = pool.get(test_key).await?;
    
    if retrieved_value != test_value {
      return Err(fred::error::RedisError::new(
        fred::error::RedisErrorKind::Unknown,
        "Health check value mismatch",
      ));
    }
    
    // Clean up test key
    let _: i64 = pool.del(test_key).await?;
    
    Ok(())
  }
}

/// External service health checker
pub struct ExternalServiceHealthChecker;

impl ExternalServiceHealthChecker {
  /// Check HTTP service health
  pub async fn check_http_service_health(
    url: &str,
    component_name: &str,
    timeout: Duration,
  ) -> HealthCheckResult {
    let start = Instant::now();
    
    match Self::perform_http_health_check(url, timeout).await {
      Ok(status_code) => {
        let response_time = start.elapsed();
        debug!("HTTP health check for {} passed with status {} in {:?}", component_name, status_code, response_time);
        
        if status_code >= 200 && status_code < 300 {
          if response_time > Duration::from_millis(2000) {
            HealthCheckResult::degraded(
              component_name.to_string(),
              format!("Slow response time: {:?}", response_time),
              response_time,
            )
          } else {
            HealthCheckResult::healthy(component_name.to_string(), response_time)
          }
        } else {
          HealthCheckResult::degraded(
            component_name.to_string(),
            format!("HTTP status code: {}", status_code),
            response_time,
          )
        }
      }
      Err(e) => {
        let response_time = start.elapsed();
        error!("HTTP health check for {} failed: {:?}", component_name, e);
        HealthCheckResult::unhealthy(
          component_name.to_string(),
          format!("HTTP request failed: {}", e),
          response_time,
        )
      }
    }
  }

  /// Perform actual HTTP health check
  async fn perform_http_health_check(url: &str, timeout: Duration) -> Result<u16, reqwest::Error> {
    let client = reqwest::Client::builder()
      .timeout(timeout)
      .build()?;
    
    let response = client.get(url).send().await?;
    Ok(response.status().as_u16())
  }
}

/// Comprehensive health checker
pub struct HealthChecker {
  mysql_pools: Vec<Pool<MySql>>,
  redis_pool: Option<fred::clients::Pool>,
  external_services: Vec<(String, String)>, // (name, url)
}

impl HealthChecker {
  pub fn new() -> Self {
    Self {
      mysql_pools: Vec::new(),
      redis_pool: None,
      external_services: Vec::new(),
    }
  }

  /// Add MySQL pool for health checking
  pub fn add_mysql_pool(&mut self, pool: Pool<MySql>) {
    self.mysql_pools.push(pool);
  }

  /// Set Redis pool for health checking
  pub fn set_redis_pool(&mut self, pool: fred::clients::Pool) {
    self.redis_pool = Some(pool);
  }

  /// Add external service for health checking
  pub fn add_external_service(&mut self, name: String, url: String) {
    self.external_services.push((name, url));
  }

  /// Perform all health checks
  pub async fn check_all_health(&self) -> Vec<HealthCheckResult> {
    let mut results = Vec::new();

    // Check MySQL databases
    for (i, pool) in self.mysql_pools.iter().enumerate() {
      let component_name = if i == 0 {
        "mysql_primary".to_string()
      } else {
        format!("mysql_shard_{}", i - 1)
      };
      
      let result = DatabaseHealthChecker::check_mysql_health(pool, &component_name).await;
      results.push(result);
    }

    // Check Redis
    if let Some(ref redis_pool) = self.redis_pool {
      let result = RedisHealthChecker::check_redis_health(redis_pool, "redis").await;
      results.push(result);
    }

    // Check external services
    for (name, url) in &self.external_services {
      let result = ExternalServiceHealthChecker::check_http_service_health(
        url,
        name,
        Duration::from_secs(5),
      ).await;
      results.push(result);
    }

    results
  }

  /// Get overall health status
  pub async fn get_overall_health_status(&self) -> HealthStatus {
    let results = self.check_all_health().await;
    
    if results.is_empty() {
      return HealthStatus::Healthy;
    }

    let unhealthy_count = results.iter().filter(|r| r.status == HealthStatus::Unhealthy).count();
    let degraded_count = results.iter().filter(|r| r.status == HealthStatus::Degraded).count();

    if unhealthy_count > 0 {
      HealthStatus::Unhealthy
    } else if degraded_count > 0 {
      HealthStatus::Degraded
    } else {
      HealthStatus::Healthy
    }
  }

  /// Start periodic health checks
  pub fn start_periodic_checks(
    &self,
    interval: Duration,
    callback: impl Fn(Vec<HealthCheckResult>) + Send + Sync + 'static,
  ) -> tokio::task::JoinHandle<()> {
    let mysql_pools = self.mysql_pools.clone();
    let redis_pool = self.redis_pool.clone();
    let external_services = self.external_services.clone();

    tokio::spawn(async move {
      let mut interval_timer = tokio::time::interval(interval);
      
      loop {
        interval_timer.tick().await;
        
        let checker = HealthChecker {
          mysql_pools: mysql_pools.clone(),
          redis_pool: redis_pool.clone(),
          external_services: external_services.clone(),
        };
        
        let results = checker.check_all_health().await;
        callback(results);
      }
    })
  }
}

impl Default for HealthChecker {
  fn default() -> Self {
    Self::new()
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_health_checker_creation() {
    let checker = HealthChecker::new();
    assert_eq!(checker.mysql_pools.len(), 0);
    assert!(checker.redis_pool.is_none());
    assert_eq!(checker.external_services.len(), 0);
  }

  #[test]
  fn test_health_checker_add_services() {
    let mut checker = HealthChecker::new();
    checker.add_external_service("test_service".to_string(), "http://example.com/health".to_string());
    
    assert_eq!(checker.external_services.len(), 1);
    assert_eq!(checker.external_services[0].0, "test_service");
    assert_eq!(checker.external_services[0].1, "http://example.com/health");
  }

  #[tokio::test]
  async fn test_external_service_health_check() {
    // Test with a known good URL (this might fail in CI without internet)
    let result = ExternalServiceHealthChecker::check_http_service_health(
      "https://httpbin.org/status/200",
      "httpbin",
      Duration::from_secs(5),
    ).await;
    
    // We can't guarantee internet access in tests, so just check the structure
    assert!(!result.component.is_empty());
    assert!(result.response_time > Duration::ZERO);
  }
}
