//! Monitoring and observability module
//!
//! This module provides comprehensive monitoring, metrics collection,
//! and observability features for the friendship service.

pub mod health_check;
pub mod metrics;
pub mod tracing_config;

use {std::{collections::HashMap,
           sync::{atomic::{AtomicU64,
                           Ordering},
                  Arc,
                  RwLock},
           time::{Duration,
                  Instant}},
     tokio::time::interval,
     tracing::{error,
               info,
               warn}};

/// Service health status
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum HealthStatus {
  Healthy,
  Degraded,
  Unhealthy,
}

impl HealthStatus {
  /// Convert to HTTP status code
  pub fn to_http_status(&self) -> u16 {
    match self {
      HealthStatus::Healthy => 200,
      HealthStatus::Degraded => 200, // Still accepting traffic
      HealthStatus::Unhealthy => 503,
    }
  }

  /// Check if service can accept traffic
  pub fn can_accept_traffic(&self) -> bool {
    matches!(self, HealthStatus::Healthy | HealthStatus::Degraded)
  }
}

/// Health check result
#[derive(Debug, <PERSON>lone)]
pub struct HealthCheckResult {
  pub status: HealthStatus,
  pub component: String,
  pub message: String,
  pub response_time: Duration,
  pub timestamp: Instant,
}

impl HealthCheckResult {
  pub fn healthy(component: String, response_time: Duration) -> Self {
    Self {
      status: HealthStatus::Healthy,
      component,
      message: "OK".to_string(),
      response_time,
      timestamp: Instant::now(),
    }
  }

  pub fn degraded(component: String, message: String, response_time: Duration) -> Self {
    Self {
      status: HealthStatus::Degraded,
      component,
      message,
      response_time,
      timestamp: Instant::now(),
    }
  }

  pub fn unhealthy(component: String, message: String, response_time: Duration) -> Self {
    Self {
      status: HealthStatus::Unhealthy,
      component,
      message,
      response_time,
      timestamp: Instant::now(),
    }
  }
}

/// Service metrics collector
#[derive(Debug)]
pub struct MetricsCollector {
  request_count: AtomicU64,
  error_count: AtomicU64,
  response_times: Arc<RwLock<Vec<Duration>>>,
  custom_metrics: Arc<RwLock<HashMap<String, f64>>>,
  start_time: Instant,
}

impl MetricsCollector {
  pub fn new() -> Self {
    Self {
      request_count: AtomicU64::new(0),
      error_count: AtomicU64::new(0),
      response_times: Arc::new(RwLock::new(Vec::new())),
      custom_metrics: Arc::new(RwLock::new(HashMap::new())),
      start_time: Instant::now(),
    }
  }

  /// Record a request
  pub fn record_request(&self, response_time: Duration, is_error: bool) {
    self.request_count.fetch_add(1, Ordering::Relaxed);
    
    if is_error {
      self.error_count.fetch_add(1, Ordering::Relaxed);
    }

    // Store response time (keep only last 1000 for memory efficiency)
    if let Ok(mut times) = self.response_times.write() {
      times.push(response_time);
      if times.len() > 1000 {
        times.remove(0);
      }
    }
  }

  /// Record custom metric
  pub fn record_custom_metric(&self, name: String, value: f64) {
    if let Ok(mut metrics) = self.custom_metrics.write() {
      metrics.insert(name, value);
    }
  }

  /// Get current metrics snapshot
  pub fn get_metrics(&self) -> ServiceMetrics {
    let request_count = self.request_count.load(Ordering::Relaxed);
    let error_count = self.error_count.load(Ordering::Relaxed);
    let uptime = self.start_time.elapsed();

    let (avg_response_time, p95_response_time, p99_response_time) = {
      if let Ok(times) = self.response_times.read() {
        if times.is_empty() {
          (Duration::ZERO, Duration::ZERO, Duration::ZERO)
        } else {
          let mut sorted_times = times.clone();
          sorted_times.sort();

          let avg = Duration::from_nanos(
            sorted_times.iter().map(|d| d.as_nanos() as u64).sum::<u64>() / sorted_times.len() as u64
          );

          let p95_index = (sorted_times.len() as f64 * 0.95) as usize;
          let p99_index = (sorted_times.len() as f64 * 0.99) as usize;

          let p95 = sorted_times.get(p95_index.min(sorted_times.len() - 1)).copied().unwrap_or(Duration::ZERO);
          let p99 = sorted_times.get(p99_index.min(sorted_times.len() - 1)).copied().unwrap_or(Duration::ZERO);

          (avg, p95, p99)
        }
      } else {
        (Duration::ZERO, Duration::ZERO, Duration::ZERO)
      }
    };

    let error_rate = if request_count > 0 {
      error_count as f64 / request_count as f64
    } else {
      0.0
    };

    let requests_per_second = if uptime.as_secs() > 0 {
      request_count as f64 / uptime.as_secs() as f64
    } else {
      0.0
    };

    let custom_metrics = self.custom_metrics.read().unwrap().clone();

    ServiceMetrics {
      request_count,
      error_count,
      error_rate,
      requests_per_second,
      avg_response_time,
      p95_response_time,
      p99_response_time,
      uptime,
      custom_metrics,
    }
  }

  /// Reset metrics (useful for testing)
  pub fn reset(&self) {
    self.request_count.store(0, Ordering::Relaxed);
    self.error_count.store(0, Ordering::Relaxed);
    if let Ok(mut times) = self.response_times.write() {
      times.clear();
    }
    if let Ok(mut metrics) = self.custom_metrics.write() {
      metrics.clear();
    }
  }
}

impl Default for MetricsCollector {
  fn default() -> Self {
    Self::new()
  }
}

/// Service metrics snapshot
#[derive(Debug, Clone)]
pub struct ServiceMetrics {
  pub request_count: u64,
  pub error_count: u64,
  pub error_rate: f64,
  pub requests_per_second: f64,
  pub avg_response_time: Duration,
  pub p95_response_time: Duration,
  pub p99_response_time: Duration,
  pub uptime: Duration,
  pub custom_metrics: HashMap<String, f64>,
}

impl ServiceMetrics {
  /// Check if metrics indicate healthy service
  pub fn is_healthy(&self) -> bool {
    self.error_rate < 0.05 && // Less than 5% error rate
    self.avg_response_time < Duration::from_millis(1000) // Average response time under 1s
  }

  /// Check if metrics indicate degraded service
  pub fn is_degraded(&self) -> bool {
    (self.error_rate >= 0.05 && self.error_rate < 0.20) || // 5-20% error rate
    (self.avg_response_time >= Duration::from_millis(1000) && self.avg_response_time < Duration::from_millis(5000)) // 1-5s response time
  }

  /// Get overall health status based on metrics
  pub fn get_health_status(&self) -> HealthStatus {
    if self.is_healthy() {
      HealthStatus::Healthy
    } else if self.is_degraded() {
      HealthStatus::Degraded
    } else {
      HealthStatus::Unhealthy
    }
  }
}

/// Monitoring manager
pub struct MonitoringManager {
  metrics_collector: Arc<MetricsCollector>,
  health_checks: Arc<RwLock<HashMap<String, HealthCheckResult>>>,
}

impl MonitoringManager {
  pub fn new() -> Self {
    Self {
      metrics_collector: Arc::new(MetricsCollector::new()),
      health_checks: Arc::new(RwLock::new(HashMap::new())),
    }
  }

  /// Get metrics collector
  pub fn metrics(&self) -> Arc<MetricsCollector> {
    Arc::clone(&self.metrics_collector)
  }

  /// Add health check result
  pub fn add_health_check(&self, result: HealthCheckResult) {
    if let Ok(mut checks) = self.health_checks.write() {
      checks.insert(result.component.clone(), result);
    }
  }

  /// Get overall health status
  pub fn get_overall_health(&self) -> HealthStatus {
    let metrics_status = self.metrics_collector.get_metrics().get_health_status();
    
    let health_check_status = if let Ok(checks) = self.health_checks.read() {
      if checks.is_empty() {
        HealthStatus::Healthy
      } else {
        let unhealthy_count = checks.values().filter(|c| c.status == HealthStatus::Unhealthy).count();
        let degraded_count = checks.values().filter(|c| c.status == HealthStatus::Degraded).count();
        
        if unhealthy_count > 0 {
          HealthStatus::Unhealthy
        } else if degraded_count > 0 {
          HealthStatus::Degraded
        } else {
          HealthStatus::Healthy
        }
      }
    } else {
      HealthStatus::Unhealthy
    };

    // Return the worst status
    match (metrics_status, health_check_status) {
      (HealthStatus::Unhealthy, _) | (_, HealthStatus::Unhealthy) => HealthStatus::Unhealthy,
      (HealthStatus::Degraded, _) | (_, HealthStatus::Degraded) => HealthStatus::Degraded,
      _ => HealthStatus::Healthy,
    }
  }

  /// Get all health check results
  pub fn get_health_checks(&self) -> HashMap<String, HealthCheckResult> {
    self.health_checks.read().unwrap().clone()
  }

  /// Start periodic health checks
  pub fn start_periodic_health_checks(&self, interval_secs: u64) -> tokio::task::JoinHandle<()> {
    let health_checks = Arc::clone(&self.health_checks);
    
    tokio::spawn(async move {
      let mut interval = interval(Duration::from_secs(interval_secs));
      
      loop {
        interval.tick().await;
        
        // Clean up old health check results (older than 5 minutes)
        if let Ok(mut checks) = health_checks.write() {
          let cutoff = Instant::now() - Duration::from_secs(300);
          checks.retain(|_, result| result.timestamp > cutoff);
        }
      }
    })
  }

  /// Start metrics logging
  pub fn start_metrics_logging(&self, interval_secs: u64) -> tokio::task::JoinHandle<()> {
    let metrics_collector = Arc::clone(&self.metrics_collector);
    
    tokio::spawn(async move {
      let mut interval = interval(Duration::from_secs(interval_secs));
      
      loop {
        interval.tick().await;
        
        let metrics = metrics_collector.get_metrics();
        info!(
          "Service metrics - Requests: {}, Errors: {}, Error rate: {:.2}%, RPS: {:.2}, Avg response: {:?}, P95: {:?}, P99: {:?}, Uptime: {:?}",
          metrics.request_count,
          metrics.error_count,
          metrics.error_rate * 100.0,
          metrics.requests_per_second,
          metrics.avg_response_time,
          metrics.p95_response_time,
          metrics.p99_response_time,
          metrics.uptime
        );

        // Log warnings for degraded performance
        if metrics.error_rate > 0.05 {
          warn!("High error rate detected: {:.2}%", metrics.error_rate * 100.0);
        }
        
        if metrics.avg_response_time > Duration::from_millis(1000) {
          warn!("High average response time detected: {:?}", metrics.avg_response_time);
        }
      }
    })
  }
}

impl Default for MonitoringManager {
  fn default() -> Self {
    Self::new()
  }
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_health_status() {
    assert_eq!(HealthStatus::Healthy.to_http_status(), 200);
    assert_eq!(HealthStatus::Degraded.to_http_status(), 200);
    assert_eq!(HealthStatus::Unhealthy.to_http_status(), 503);

    assert!(HealthStatus::Healthy.can_accept_traffic());
    assert!(HealthStatus::Degraded.can_accept_traffic());
    assert!(!HealthStatus::Unhealthy.can_accept_traffic());
  }

  #[test]
  fn test_metrics_collector() {
    let collector = MetricsCollector::new();
    
    // Record some requests
    collector.record_request(Duration::from_millis(100), false);
    collector.record_request(Duration::from_millis(200), true);
    collector.record_request(Duration::from_millis(150), false);

    let metrics = collector.get_metrics();
    assert_eq!(metrics.request_count, 3);
    assert_eq!(metrics.error_count, 1);
    assert!((metrics.error_rate - 0.333).abs() < 0.01);
  }

  #[test]
  fn test_service_metrics_health() {
    let metrics = ServiceMetrics {
      request_count: 100,
      error_count: 2,
      error_rate: 0.02,
      requests_per_second: 10.0,
      avg_response_time: Duration::from_millis(500),
      p95_response_time: Duration::from_millis(800),
      p99_response_time: Duration::from_millis(1200),
      uptime: Duration::from_secs(3600),
      custom_metrics: HashMap::new(),
    };

    assert!(metrics.is_healthy());
    assert_eq!(metrics.get_health_status(), HealthStatus::Healthy);
  }

  #[test]
  fn test_monitoring_manager() {
    let manager = MonitoringManager::new();
    
    // Add a health check
    let health_check = HealthCheckResult::healthy("database".to_string(), Duration::from_millis(50));
    manager.add_health_check(health_check);

    let overall_health = manager.get_overall_health();
    assert_eq!(overall_health, HealthStatus::Healthy);

    let health_checks = manager.get_health_checks();
    assert_eq!(health_checks.len(), 1);
    assert!(health_checks.contains_key("database"));
  }
}
