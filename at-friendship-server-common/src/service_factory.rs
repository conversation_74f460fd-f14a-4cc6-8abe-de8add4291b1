//! Service factory for creating optimized service instances
//!
//! This module provides a centralized factory for creating all service
//! components with proper configuration, error handling, and monitoring.

use {crate::config::{AppConfig,
                     Environment},
     at_friendship_domain_model::{all::FriendshipError,
                                  validation::phone_validation::PhoneNumberValidator},
     at_friendship_repo::repo::{base_repository::BaseRepositoryImpl,
                                db_config::{DbConnectionBuilder,
                                            DbPoolConfig}},
     fred::{clients::Pool as RedisPool,
            prelude::*,
            types::config::Config as RedisConfig},
     sqlx::{MySql,
            Pool},
     std::{sync::Arc,
           time::Duration},
     tracing::{error,
               info}};

/// Service factory for creating optimized service instances
pub struct ServiceFactory {
  config: AppConfig,
  mysql_pool: Option<Pool<MySql>>,
  tidb_pools: Option<Vec<Pool<MySql>>>,
  redis_pool: Option<RedisPool>,
}

impl ServiceFactory {
  /// Create a new service factory with configuration
  pub fn new() -> Result<Self, FriendshipError> {
    let config = AppConfig::load().map_err(|e| {
      error!("Failed to load application configuration: {:?}", e);
      FriendshipError::Unknown(format!("Configuration error: {}", e))
    })?;

    config.validate().map_err(|e| {
      error!("Configuration validation failed: {:?}", e);
      FriendshipError::Unknown(format!("Invalid configuration: {}", e))
    })?;

    info!("Service factory initialized with environment: {:?}", config.environment);

    Ok(Self {
      config,
      mysql_pool: None,
      tidb_pools: None,
      redis_pool: None,
    })
  }

  /// Initialize MySQL connection pool
  pub async fn init_mysql_pool(&mut self) -> Result<(), FriendshipError> {
    if self.mysql_pool.is_some() {
      return Ok(()); // Already initialized
    }

    let db_config = match self.config.environment {
      Environment::Testing => DbPoolConfig::testing(),
      Environment::Development => DbPoolConfig::development(),
      Environment::Staging => DbPoolConfig::production(), // Use production config for staging
      Environment::Production => DbPoolConfig::production(),
    };

    let pool = DbConnectionBuilder::create_mysql_pool(&self.config.database.url, db_config)
      .await
      .map_err(|e| {
        error!("Failed to create MySQL connection pool: {:?}", e);
        FriendshipError::DatabaseError(format!("MySQL pool creation failed: {}", e))
      })?;

    // Test pool health
    DbConnectionBuilder::test_pool_health(&pool).await.map_err(|e| {
      error!("MySQL pool health check failed: {:?}", e);
      FriendshipError::DatabaseError("MySQL pool health check failed".to_string())
    })?;

    self.mysql_pool = Some(pool);
    info!("MySQL connection pool initialized successfully");
    Ok(())
  }

  /// Initialize TiDB sharded connection pools
  pub async fn init_tidb_pools(&mut self, shard_configs: Vec<(String, DbPoolConfig)>) -> Result<(), FriendshipError> {
    if self.tidb_pools.is_some() {
      return Ok(()); // Already initialized
    }

    let pools = DbConnectionBuilder::create_sharded_pools(shard_configs)
      .await
      .map_err(|e| {
        error!("Failed to create TiDB sharded pools: {:?}", e);
        FriendshipError::DatabaseError(format!("TiDB pools creation failed: {}", e))
      })?;

    // Test all pool health
    for (i, pool) in pools.iter().enumerate() {
      DbConnectionBuilder::test_pool_health(pool).await.map_err(|e| {
        error!("TiDB shard {} pool health check failed: {:?}", i, e);
        FriendshipError::DatabaseError(format!("TiDB shard {} health check failed", i))
      })?;
    }

    self.tidb_pools = Some(pools);
    info!("TiDB sharded connection pools initialized successfully");
    Ok(())
  }

  /// Initialize Redis connection pool
  pub async fn init_redis_pool(&mut self) -> Result<(), FriendshipError> {
    if self.redis_pool.is_some() {
      return Ok(()); // Already initialized
    }

    let redis_config = RedisConfig::from_url(&self.config.redis.url).map_err(|e| {
      error!("Invalid Redis URL configuration: {:?}", e);
      FriendshipError::RedisError(format!("Redis config error: {}", e))
    })?;

    let pool = RedisPool::new(
      redis_config,
      None,
      None,
      None,
      self.config.redis.pool_size as usize,
    )
    .map_err(|e| {
      error!("Failed to create Redis pool: {:?}", e);
      FriendshipError::RedisError(format!("Redis pool creation failed: {}", e))
    })?;

    pool.connect_pool();
    pool.wait_for_connect().await.map_err(|e| {
      error!("Failed to connect to Redis: {:?}", e);
      FriendshipError::RedisError(format!("Redis connection failed: {}", e))
    })?;

    self.redis_pool = Some(pool);
    info!("Redis connection pool initialized successfully");
    Ok(())
  }

  /// Get MySQL pool (initialize if needed)
  pub async fn get_mysql_pool(&mut self) -> Result<&Pool<MySql>, FriendshipError> {
    if self.mysql_pool.is_none() {
      self.init_mysql_pool().await?;
    }
    Ok(self.mysql_pool.as_ref().unwrap())
  }

  /// Get TiDB pools (initialize if needed)
  pub async fn get_tidb_pools(&mut self, shard_configs: Vec<(String, DbPoolConfig)>) -> Result<&Vec<Pool<MySql>>, FriendshipError> {
    if self.tidb_pools.is_none() {
      self.init_tidb_pools(shard_configs).await?;
    }
    Ok(self.tidb_pools.as_ref().unwrap())
  }

  /// Get Redis pool (initialize if needed)
  pub async fn get_redis_pool(&mut self) -> Result<&RedisPool, FriendshipError> {
    if self.redis_pool.is_none() {
      self.init_redis_pool().await?;
    }
    Ok(self.redis_pool.as_ref().unwrap())
  }

  /// Create optimized repository instances
  pub async fn create_repositories(&mut self) -> Result<RepositoryInstances, FriendshipError> {
    let mysql_pool = self.get_mysql_pool().await?.clone();

    // Create base repositories
    let botim_user_repo = BaseRepositoryImpl::new(mysql_pool.clone(), "botim_user");
    let blocked_user_repo = BaseRepositoryImpl::new(mysql_pool.clone(), "blocked_user");
    let contact_repo = BaseRepositoryImpl::new(mysql_pool.clone(), "contact");
    let notification_repo = BaseRepositoryImpl::new(mysql_pool.clone(), "notification");

    // Create validators
    let phone_validator = PhoneNumberValidator::new();

    Ok(RepositoryInstances {
      botim_user_repo,
      blocked_user_repo,
      contact_repo,
      notification_repo,
      phone_validator,
    })
  }

  /// Get application configuration
  pub fn get_config(&self) -> &AppConfig {
    &self.config
  }

  /// Start monitoring tasks
  pub fn start_monitoring(&self) -> Vec<tokio::task::JoinHandle<()>> {
    let mut handles = Vec::new();

    // Monitor MySQL pool if initialized
    if let Some(ref pool) = self.mysql_pool {
      let pool_clone = pool.clone();
      let handle = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(60));
        loop {
          interval.tick().await;
          if let Err(e) = DbConnectionBuilder::test_pool_health(&pool_clone).await {
            error!("MySQL pool health check failed: {:?}", e);
          }
        }
      });
      handles.push(handle);
    }

    // Monitor TiDB pools if initialized
    if let Some(ref pools) = self.tidb_pools {
      for (i, pool) in pools.iter().enumerate() {
        let pool_clone = pool.clone();
        let shard_index = i;
        let handle = tokio::spawn(async move {
          let mut interval = tokio::time::interval(Duration::from_secs(60));
          loop {
            interval.tick().await;
            if let Err(e) = DbConnectionBuilder::test_pool_health(&pool_clone).await {
              error!("TiDB shard {} pool health check failed: {:?}", shard_index, e);
            }
          }
        });
        handles.push(handle);
      }
    }

    info!("Started {} monitoring tasks", handles.len());
    handles
  }
}

/// Container for all repository instances
pub struct RepositoryInstances {
  pub botim_user_repo: BaseRepositoryImpl,
  pub blocked_user_repo: BaseRepositoryImpl,
  pub contact_repo: BaseRepositoryImpl,
  pub notification_repo: BaseRepositoryImpl,
  pub phone_validator: PhoneNumberValidator,
}

#[cfg(test)]
mod tests {
  use super::*;

  #[tokio::test]
  async fn test_service_factory_creation() {
    // This test would require proper environment setup
    // For now, just test that the structure is correct
    assert_eq!(
      std::mem::size_of::<ServiceFactory>(),
      std::mem::size_of::<AppConfig>() + 
      std::mem::size_of::<Option<Pool<MySql>>>() * 2 +
      std::mem::size_of::<Option<RedisPool>>()
    );
  }
}
