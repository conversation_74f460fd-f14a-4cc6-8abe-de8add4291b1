[package]
name = "at-friendship-server-grpc"
version.workspace = true
edition.workspace = true
authors.workspace = true
repository.workspace = true
homepage.workspace = true
description.workspace = true
license.workspace = true


[[bin]]
name = "at_friendship_server_grpc"
path = "src/main.rs"

[dependencies]
# dep-mgmt
at-deps = { workspace = true }
at-internal-deps = { workspace = true }
at-janus-server = { workspace = true }
at-janus-client = { workspace = true }

at-friendship-server-common = { workspace = true }
at-friendship-integration = { workspace = true }
at-friendship-domain-model = { workspace = true }

## exceptional
serde = { workspace = true }
sqlx = { workspace = true }
tonic = { workspace = true }
lapin = { workspace = true }
fred = { workspace = true }
tracing = { workspace = true }
