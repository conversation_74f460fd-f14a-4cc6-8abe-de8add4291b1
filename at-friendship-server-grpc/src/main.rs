use {at_deps::at_friendship::*,
     at_internal_deps::at_friendship::*};

use {at_ac_integration::prelude::*,
     at_otel_integration::prelude::*,
     std::sync::Arc,
     tracing::{debug,
               info}};

use at_friendship_server_grpc::prelude::*;

const APP_ID: &str = "at-friendship-server-grpc";

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
  // using AutoUpdateApolloConfigLoader to load the log configuration
  // read the log.json on Apollo Server
  let _guard = OtelLogAdapter::init_with_apollo_config().await?;

  // load the api server config
  let api_server_config_loader = AutoUpdateApolloConfigLoader::app_singleton();
  api_server_config_loader.auto_update().await?;
  let api_server_config = api_server_config_loader.config_snapshot().await;
  let api_server_config = api_server_config.configuration().deserialize::<ApiServerConfig>()?;

  let mq_server_config_loader = AutoUpdateApolloConfigLoader::mq_singleton();
  mq_server_config_loader.auto_update().await?;

  debug!("loaded config: {:?}", api_server_config);

  info!(
    "{} gRPC API server listening on: {}",
    APP_ID,
    api_server_config.server_address()
  );

  let server = Arc::new(JanusServer::new(api_server_config_loader).await?);
  // init the mq server
  let _handler = match start_mq_server(mq_server_config_loader.clone(), server.clone()).await {
    Ok(handler) => {
      info!("MQ server started successfully");
      Some(handler)
    }
    Err(e) => {
      tracing::error!("Failed to start MQ server: {:?}", e);
      tracing::warn!("Continuing without MQ server...");
      None
    }
  };
  // init the grpc server
  let janus_server = create_janus_server(server.clone()).await?;
  janus_server.start(api_server_config.server_address()).await?;

  Ok(())
}
