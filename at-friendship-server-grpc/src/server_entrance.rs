use {async_trait::async_trait,
     at_ac_integration::prelude::{AutoUpdate,
                                  AutoUpdateApolloConfigLoader},
     at_deps::{at_friendship::*,
               atam::anyhow},
     at_friendship_domain_model::errors::FriendshipError,
     at_friendship_server_common::prelude::ServerFunction,
     at_hermes::prelude::*,
     at_internal_deps::at_friendship::*,
     at_janus_client::prelude::*,
     at_janus_server::prelude::{self as janus,
                                JanusServerFactory},
     at_lib_event::prelude::*,
     fred::{clients::Pool as RedisPool,
            prelude::*,
            types::config::Config as RedisConfig},
     std::sync::Arc,
     tracing::{error,
               info}};

#[derive(Clone)]
pub struct JanusServer(ServerFunction);

impl std::ops::Deref for JanusServer {
  type Target = ServerFunction;

  fn deref(&self) -> &Self::Target {
    &self.0
  }
}

impl JanusServer {
  pub async fn new(config_loader: &'static AutoUpdateApolloConfigLoader) -> anyhow::Result<Self> {
    // Load configuration
    let app_config = config_loader.config_snapshot().await;

    // MySQL pool for legacy tables (botim_users, blocked_users, etc.)
    let friendship_db_url = app_config
      .configuration()
      .get_string("friendship_db_url")?
      .ok_or(anyhow::anyhow!("friendship_db_url is not set"))?;
    let mysql_pool = sqlx::MySqlPool::connect(&friendship_db_url).await?;
    info!("MySQL pool connected for legacy tables");

    // Load required TiDB sharded database configurations
    let configs = app_config
      .configuration()
      .get_array("friendship_sharded_tidb_configs")?
      .ok_or(anyhow::anyhow!("friendship_sharded_tidb_configs is required"))?;

    info!("Loading TiDB sharded configuration with {} shards", configs.len());
    let mut tidb_pools = Vec::with_capacity(configs.len());

    for config in configs {
      let config_obj = config
        .as_object()
        .ok_or(anyhow::anyhow!("Invalid shard configuration format"))?;

      let db_url = config_obj
        .get("db_url")
        .and_then(|v| v.as_str())
        .ok_or(anyhow::anyhow!("Missing db_url in shard configuration"))?;

      let shard_id = config_obj
        .get("shard_id")
        .and_then(|v| v.as_u64())
        .ok_or(anyhow::anyhow!("Missing shard_id in shard configuration"))?;

      let pool = sqlx::MySqlPool::connect(db_url).await?;
      info!("TiDB shard {} connected", shard_id);
      tidb_pools.push(pool);
    }

    let friendship_redis_url = app_config
      .configuration()
      .get_string("friendship_redis_url")?
      .ok_or(anyhow::anyhow!("friendship_redis_url is not set"))?;

    let redis_config = RedisConfig::from_url(&friendship_redis_url).map_err(|e| {
      error!("redis_config error: {:?}", e);
      anyhow::anyhow!("redis_config error: {:?}", e)
    })?;

    let redis_pool = RedisPool::new(redis_config, None, None, None, 6).map_err(|e| {
      error!("redis_pool error: {:?}", e);
      anyhow::anyhow!("redis_pool error: {:?}", e)
    })?;

    redis_pool.connect_pool();
    redis_pool.wait_for_connect().await?;

    let janus_client_factory = JanusClientFactory::new_with_default_config_loader().await?;
    let janus_client = janus_client_factory.create_janus_client().await?;

    let server_function = ServerFunction::new(mysql_pool, tidb_pools, janus_client, redis_pool).await;
    Ok(Self(server_function))
  }
}

#[async_trait]
impl janus::ApplicationMessageProcessor for JanusServer {
  async fn process_fire_and_forget(&self, app_msg: &ApplicationMessage) -> janus::JanusServerResult<()> {
    // Example implementation - just logs the message and returns None
    let _: ApplicationMessage = self.dispatch_server_function(app_msg).await.map_err(|e| {
      error!("process_fire_and_forget error: {:?}", e);
      janus::JanusServerError::InternalServerError(format!(
        "dispatch fire and forget request to server function error: {e:?}"
      ))
    })?;
    info!(
      "Processing fire and forget (Offline message delivery) message: {:?}",
      app_msg.id.to_string()
    );
    Ok(())
  }

  async fn process_request(&self, app_msg: &ApplicationMessage) -> janus::JanusServerResult<ApplicationMessage> {
    // Example implementation - just logs the message and returns None
    info!("Processing request: {:?}", app_msg.id.to_string());
    // In a real implementation, you would process the message and return a response if needed
    let app_msg_resp: ApplicationMessage = self.dispatch_server_function(app_msg).await.map_err(|e| {
      error!("dispatch_server_function error: {:?}", e);
      janus::JanusServerError::InternalServerError(format!("dispatch request to server function error: {e:?}"))
    })?;
    Ok(app_msg_resp)
  }
}

pub struct FriendshipJanusServerMeta;
impl janus::ServiceMeta for FriendshipJanusServerMeta {
  const SERVICE_NAME: &'static str = "at-friendship-server-grpc";
  const SERVICE_TAGS: &'static [&'static str] = &["development"];
  const SERVICE_VERSION: &'static str = "1.0.0";
}

pub async fn create_janus_server(
  server: Arc<JanusServer>,
) -> anyhow::Result<janus::JanusServer<FriendshipJanusServerMeta>> {
  let janus_server_factory = JanusServerFactory::new_with_default_config_loader().await?;
  let janus_server = janus_server_factory
    .create_janus_server_with_etcd_registration::<FriendshipJanusServerMeta>(server)
    .await?;
  Ok(janus_server)
}

#[async_trait]
impl EventHandler for JanusServer {
  async fn handle_event(&self, delivery: EventDelivery) -> std::result::Result<MessageHandlingResult, EventError> {
    info!("Received message with routing key: {}", delivery.routing_key);
    // Parse delivery.data as Json
    let payload_str = match String::from_utf8(delivery.data) {
      Ok(str) => str,
      Err(e) => {
        error!(
          "Failed to parse message data: {} for routing key: {}",
          e, delivery.routing_key
        );
        return Ok(MessageHandlingResult::Reject { requeue: false });
      }
    };
    match self.dispatch_mq_server_function(&payload_str).await {
      Ok(_) => {
        info!("Successfully processed mq message");
        Ok(MessageHandlingResult::Ack)
      }
      Err(FriendshipError::MqDeserialization(e)) => {
        error!(
          "Failed to deserialize message: {:?} for routing key: {}",
          e, delivery.routing_key
        );
        Ok(MessageHandlingResult::Reject { requeue: false })
      }
      Err(e) => {
        error!(
          "Failed to process message: {:?} for routing key: {}",
          e, delivery.routing_key
        );
        Ok(MessageHandlingResult::Reject { requeue: true })
      }
    }
  }
}

/// Example of how to create a DfServer using the template
pub async fn start_mq_server(
  mq_config_loader: AutoUpdateApolloConfigLoader,
  server: Arc<JanusServer>,
) -> anyhow::Result<tokio::task::JoinHandle<()>> {
  let rabbit_config = mq_config_loader.config_snapshot().await;
  let rabbit_url = rabbit_config
    .configuration()
    .get_string("rabbitmq_url")?
    .ok_or(anyhow::anyhow!("rabbitmq_url is not set"))?;
  let exchange_name = rabbit_config
    .configuration()
    .get_string("rabbitmq_exchange")?
    .ok_or(anyhow::anyhow!("rabbitmq_exchange is not set"))?;
  let service_name = rabbit_config
    .configuration()
    .get_string("service_name")?
    .ok_or(anyhow::anyhow!("service_name is not set"))?;

  let consumer = RabbitEventConsumer::new(ConsumerConfig {
    connection_string: rabbit_url,
    exchange_name,
    queue_name: service_name,
    batch_size: 1,
    ..Default::default()
  })?;

  let handle = consumer.start(server.clone()).await?;

  info!("MQ server started for config: {:?}", rabbit_config);
  Ok(handle)
}
