use {at_deps::at_friendship::*,
     getset::Get<PERSON>,
     serde::Deserialize};

#[derive(Deserialize, Get<PERSON>, Debug)]
#[getset(get = "pub")]
pub struct ShardDbConfig {
  /// Shard ID (1-16)
  shard_id: u8,
  /// Database URL for this shard
  db_url:   String,
}

#[derive(Deserialize, Getters, Debug)]
#[getset(get = "pub")]
pub struct ApiServerConfig {
  /// The address to listen on for the gRPC API server.
  server_address:                  String,
  /// The URL of the MySQL friendship database (for botim_users, blocked_users, etc.)
  friendship_db_url:               String,
  /// The URL of the friendship Redis database.
  friendship_redis_url:            String,
  /// TiDB sharded database configurations for contacts table (16 shards)
  friendship_sharded_tidb_configs: Vec<ShardDbConfig>,
}
